'use client';

import React, { useState } from 'react';
import Head from 'next/head';
import { useRouter } from 'next/router';
import Link from 'next/link';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { Mail, Lock } from 'lucide-react';
import { useAuthStore } from '@/store/auth.store';
import { useAppStore } from '@/store/app.store';
import { apiClient } from '@/lib/api';
import Button from '@/components/ui/Button';
import Input from '@/components/ui/Input';

const loginSchema = z.object({
  email: z.string().email('Please enter a valid email address'),
  password: z.string().min(6, 'Password must be at least 6 characters'),
  rememberMe: z.boolean().optional(),
});

type LoginForm = z.infer<typeof loginSchema>;

export default function LoginPage() {
  const router = useRouter();
  const { login } = useAuthStore();
  const { addNotification } = useAppStore();
  const [isLoading, setIsLoading] = useState(false);

  const {
    register,
    handleSubmit,
    formState: { errors },
  } = useForm<LoginForm>({
    resolver: zodResolver(loginSchema),
  });

  const onSubmit = async (data: LoginForm) => {
    setIsLoading(true);
    
    try {
      // Simulate API call for demo
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // Mock successful login
      const mockUser = {
        id: '1',
        email: data.email,
        name: 'Demo User',
        subscription: 'PREMIUM' as const,
        role: 'USER' as const,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      };

      const mockProfiles = [
        {
          id: '1',
          name: 'Main Profile',
          isKid: false,
          language: 'en',
          maturityLevel: 'ADULT',
          userId: '1',
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
        }
      ];
      
      // Store auth token
      localStorage.setItem('auth-token', 'demo-token');
      
      // Update auth store
      login(mockUser, mockProfiles);
      
      addNotification({
        type: 'success',
        title: 'Welcome back!',
        message: `Logged in as ${mockUser.name}`,
      });

      // Redirect to dashboard
      router.push('/dashboard');
    } catch (error: any) {
      addNotification({
        type: 'error',
        title: 'Login Failed',
        message: 'Invalid email or password',
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div>
      <Head>
        <title>Sign In - HyperFlix</title>
      </Head>

      <div className="min-h-screen bg-gradient-to-br from-black via-gray-900 to-red-900 flex items-center justify-center p-4">
        <div className="w-full max-w-md">
          {/* Logo */}
          <div className="text-center mb-8">
            <div className="w-16 h-16 bg-red-600 rounded-lg flex items-center justify-center mx-auto mb-4">
              <span className="text-white font-bold text-2xl">H</span>
            </div>
            <h1 className="text-white text-3xl font-bold">HyperFlix</h1>
            <p className="text-gray-400 mt-2">Sign in to your account</p>
          </div>

          {/* Login Form */}
          <div className="bg-black/50 backdrop-blur-md rounded-2xl p-8 border border-gray-800">
            <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
              <Input
                {...register('email')}
                type="email"
                placeholder="Email address"
                leftIcon={<Mail className="w-4 h-4" />}
                error={errors.email?.message}
                disabled={isLoading}
              />

              <Input
                {...register('password')}
                type="password"
                placeholder="Password"
                leftIcon={<Lock className="w-4 h-4" />}
                showPasswordToggle
                error={errors.password?.message}
                disabled={isLoading}
              />

              <div className="flex items-center justify-between">
                <label className="flex items-center">
                  <input
                    {...register('rememberMe')}
                    type="checkbox"
                    className="w-4 h-4 text-red-600 bg-gray-800 border-gray-600 rounded focus:ring-red-500 focus:ring-2"
                    disabled={isLoading}
                  />
                  <span className="ml-2 text-sm text-gray-300">Remember me</span>
                </label>

                <Link
                  href="/auth/forgot-password"
                  className="text-sm text-red-400 hover:text-red-300 transition-colors"
                >
                  Forgot password?
                </Link>
              </div>

              <Button
                type="submit"
                variant="primary"
                size="lg"
                loading={isLoading}
                className="w-full"
              >
                Sign In
              </Button>
            </form>

            {/* Demo Info */}
            <div className="mt-6 p-4 bg-blue-900/20 border border-blue-700 rounded-lg">
              <p className="text-blue-300 text-sm mb-2">Demo Mode:</p>
              <p className="text-blue-200 text-xs">Use any email and password to login</p>
            </div>

            {/* Sign Up Link */}
            <p className="text-center text-gray-400 mt-6">
              Don't have an account?{' '}
              <Link
                href="/auth/register"
                className="text-red-400 hover:text-red-300 transition-colors font-medium"
              >
                Sign up
              </Link>
            </p>
          </div>

          {/* Footer */}
          <div className="text-center mt-8 text-gray-500 text-sm">
            <p>© 2024 HyperFlix. All rights reserved.</p>
          </div>
        </div>
      </div>
    </div>
  );
}
