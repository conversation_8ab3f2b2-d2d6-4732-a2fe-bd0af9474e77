import { create } from 'zustand';
import { persist } from 'zustand/middleware';

interface User {
  id: string;
  email: string;
  name: string;
  avatar?: string;
  subscription: 'FREE' | 'BASIC' | 'PREMIUM' | 'FAMILY';
  role: 'USER' | 'ADMIN' | 'CONTENT_MANAGER';
  createdAt: string;
  updatedAt: string;
}

interface Profile {
  id: string;
  name: string;
  avatar?: string;
  isKid: boolean;
  language: string;
  maturityLevel: string;
  userId: string;
  createdAt: string;
  updatedAt: string;
}

interface AuthState {
  user: User | null;
  profiles: Profile[];
  currentProfile: Profile | null;
  isLoading: boolean;
  isAuthenticated: boolean;
}

interface AuthStore extends AuthState {
  // Actions
  setUser: (user: User | null) => void;
  setProfiles: (profiles: Profile[]) => void;
  setCurrentProfile: (profile: Profile | null) => void;
  setLoading: (loading: boolean) => void;
  login: (user: User, profiles: Profile[]) => void;
  logout: () => void;
  switchProfile: (profile: Profile) => void;
}

export const useAuthStore = create<AuthStore>()(
  persist(
    (set, get) => ({
      // Initial state
      user: null,
      profiles: [],
      currentProfile: null,
      isLoading: false,
      isAuthenticated: false,

      // Actions
      setUser: (user) => set({ user, isAuthenticated: !!user }),
      
      setProfiles: (profiles) => set({ profiles }),
      
      setCurrentProfile: (profile) => set({ currentProfile: profile }),
      
      setLoading: (loading) => set({ isLoading: loading }),
      
      login: (user, profiles) => set({
        user,
        profiles,
        isAuthenticated: true,
        isLoading: false,
        currentProfile: profiles.length > 0 ? profiles[0] : null,
      }),
      
      logout: () => set({
        user: null,
        profiles: [],
        currentProfile: null,
        isAuthenticated: false,
        isLoading: false,
      }),
      
      switchProfile: (profile) => set({ currentProfile: profile }),
    }),
    {
      name: 'auth-storage',
      partialize: (state) => ({
        user: state.user,
        profiles: state.profiles,
        currentProfile: state.currentProfile,
        isAuthenticated: state.isAuthenticated,
      }),
    }
  )
);
