import { Injectable } from '@nestjs/common';
import { PassportStrategy } from '@nestjs/passport';
import { Strategy, VerifyCallback } from 'passport-google-oauth20';
import { ConfigService } from '@nestjs/config';
import { UsersService } from '../../users/users.service';

@Injectable()
export class GoogleStrategy extends PassportStrategy(Strategy, 'google') {
  constructor(
    private readonly configService: ConfigService,
    private readonly usersService: UsersService,
  ) {
    const clientID = configService.get('GOOGLE_CLIENT_ID');
    const clientSecret = configService.get('GOOGLE_CLIENT_SECRET');

    if (!clientID || !clientSecret) {
      // Skip Google OAuth if credentials are not configured
      return;
    }

    super({
      clientID,
      clientSecret,
      callbackURL: configService.get('GOOGLE_CALLBACK_URL', 'http://localhost:3001/api/v1/auth/google/callback'),
      scope: ['email', 'profile'],
    });
  }

  async validate(
    accessToken: string,
    refreshToken: string,
    profile: any,
    done: VerifyCallback,
  ): Promise<any> {
    const { id, name, emails, photos } = profile;
    
    try {
      // Check if user exists with Google ID
      let user = await this.usersService.findByGoogleId(id);
      
      if (!user) {
        // Check if user exists with email
        const email = emails[0].value;
        user = await this.usersService.findByEmail(email);
        
        if (user) {
          // Link Google account to existing user
          user = await this.usersService.linkGoogleAccount(user.id, id);
        } else {
          // Create new user
          user = await this.usersService.create({
            email,
            googleId: id,
            firstName: name.givenName,
            lastName: name.familyName,
            avatar: photos[0]?.value,
            emailVerified: new Date(), // Google accounts are pre-verified
          });
        }
      }
      
      done(null, user);
    } catch (error) {
      done(error, null);
    }
  }
}
