import type { <PERSON>ada<PERSON> } from "next";
import { Inter } from "next/font/google";
import "./globals.css";
import { Providers } from "./providers";

const inter = Inter({ subsets: ["latin"] });

export const metadata: Metadata = {
  title: "HyperFlix - Stream Movies & TV Shows",
  description: "Watch unlimited movies and TV shows with HyperFlix. Stream in HD with AI-powered recommendations.",
  keywords: "streaming, movies, tv shows, entertainment, netflix alternative",
  authors: [{ name: "HyperFlix Team" }],
  viewport: "width=device-width, initial-scale=1",
  themeColor: "#dc2626",
  icons: {
    icon: "/favicon.ico",
    apple: "/apple-touch-icon.png",
  },
  openGraph: {
    title: "HyperFlix - Stream Movies & TV Shows",
    description: "Watch unlimited movies and TV shows with HyperFlix",
    type: "website",
    locale: "en_US",
    siteName: "HyperFlix",
  },
  twitter: {
    card: "summary_large_image",
    title: "HyperFlix - Stream Movies & TV Shows",
    description: "Watch unlimited movies and TV shows with HyperFlix",
  },
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en" className="dark">
      <body className={`${inter.className} antialiased bg-black text-white`}>
        <Providers>
          {children}
        </Providers>
      </body>
    </html>
  );
}
