'use client';

import React, { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import { Play, Plus, Info, Star, Clock, Download } from 'lucide-react';
import { useAuthStore } from '@/store/auth.store';
import { useAppStore } from '@/store/app.store';
import { apiClient } from '@/lib/api';
import { Content, Recommendation } from '@/types';
import Header from '@/components/layout/Header';
import Button from '@/components/ui/Button';
import { formatDuration, getImageUrl } from '@/lib/utils';

export default function DashboardPage() {
  const router = useRouter();
  const { isAuthenticated, currentProfile } = useAuthStore();
  const { addNotification } = useAppStore();
  
  const [featuredContent, setFeaturedContent] = useState<Content | null>(null);
  const [recommendations, setRecommendations] = useState<Recommendation[]>([]);
  const [popularContent, setPopularContent] = useState<Content[]>([]);
  const [recentContent, setRecentContent] = useState<Content[]>([]);
  const [continueWatching, setContinueWatching] = useState<Content[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    if (!isAuthenticated || !currentProfile) {
      router.push('/auth/login');
      return;
    }

    loadDashboardData();
  }, [isAuthenticated, currentProfile, router]);

  const loadDashboardData = async () => {
    try {
      setIsLoading(true);

      // Load all data in parallel
      const [
        popularResponse,
        recentResponse,
        recommendationsResponse,
        continueWatchingResponse,
      ] = await Promise.all([
        apiClient.content.getPopular({ limit: 20 }),
        apiClient.content.getRecent({ limit: 20 }),
        currentProfile ? apiClient.ai.getRecommendations(currentProfile.id, { limit: 20 }) : Promise.resolve([]),
        currentProfile ? apiClient.ai.getContinueWatching(currentProfile.id) : Promise.resolve([]),
      ]);

      setPopularContent(popularResponse.data || []);
      setRecentContent(recentResponse.data || []);
      setRecommendations(recommendationsResponse.data || []);
      setContinueWatching(continueWatchingResponse.data || []);

      // Set featured content (first popular item)
      if (popularResponse.data && popularResponse.data.length > 0) {
        setFeaturedContent(popularResponse.data[0]);
      }

    } catch (error: any) {
      console.error('Error loading dashboard data:', error);
      addNotification({
        type: 'error',
        title: 'Error',
        message: 'Failed to load content. Please try again.',
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handlePlayContent = (content: Content) => {
    router.push(`/watch/${content.id}`);
  };

  const handleContentDetails = (content: Content) => {
    router.push(`/content/${content.id}`);
  };

  const ContentCard = ({ content, size = 'medium' }: { content: Content; size?: 'small' | 'medium' | 'large' }) => {
    const sizeClasses = {
      small: 'w-32 h-48',
      medium: 'w-40 h-60',
      large: 'w-48 h-72',
    };

    return (
      <div className={`${sizeClasses[size]} flex-shrink-0 group cursor-pointer transition-transform hover:scale-105`}>
        <div className="relative w-full h-full rounded-lg overflow-hidden bg-gray-800">
          <img
            src={getImageUrl(content.poster)}
            alt={content.title}
            className="w-full h-full object-cover"
            onError={(e) => {
              e.currentTarget.src = '/images/placeholder-poster.jpg';
            }}
          />
          
          {/* Overlay */}
          <div className="absolute inset-0 bg-black/60 opacity-0 group-hover:opacity-100 transition-opacity flex items-center justify-center">
            <div className="text-center space-y-2">
              <Button
                size="sm"
                onClick={() => handlePlayContent(content)}
                leftIcon={<Play className="w-4 h-4" />}
              >
                Play
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={() => handleContentDetails(content)}
                leftIcon={<Info className="w-4 h-4" />}
              >
                Info
              </Button>
            </div>
          </div>

          {/* Rating */}
          {content.averageRating && (
            <div className="absolute top-2 right-2 bg-black/80 rounded px-2 py-1 flex items-center space-x-1">
              <Star className="w-3 h-3 text-yellow-400 fill-current" />
              <span className="text-white text-xs">{content.averageRating.toFixed(1)}</span>
            </div>
          )}
        </div>
        
        <div className="mt-2">
          <h3 className="text-white text-sm font-medium truncate">{content.title}</h3>
          <p className="text-gray-400 text-xs">{content.releaseDate ? new Date(content.releaseDate).getFullYear() : ''}</p>
        </div>
      </div>
    );
  };

  const ContentRow = ({ title, items, loading = false }: { title: string; items: Content[]; loading?: boolean }) => (
    <div className="mb-8">
      <h2 className="text-white text-xl font-semibold mb-4">{title}</h2>
      <div className="flex space-x-4 overflow-x-auto scrollbar-hide pb-4">
        {loading ? (
          Array.from({ length: 6 }).map((_, index) => (
            <div key={index} className="w-40 h-60 bg-gray-800 rounded-lg animate-pulse flex-shrink-0" />
          ))
        ) : items.length > 0 ? (
          items.map((content) => (
            <ContentCard key={content.id} content={content} />
          ))
        ) : (
          <p className="text-gray-400">No content available</p>
        )}
      </div>
    </div>
  );

  if (isLoading) {
    return (
      <div className="min-h-screen bg-black">
        <Header />
        <div className="pt-16 flex items-center justify-center min-h-screen">
          <div className="text-center">
            <div className="w-16 h-16 border-4 border-gray-300 border-t-red-600 rounded-full animate-spin mx-auto mb-4"></div>
            <p className="text-white">Loading your personalized content...</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-black">
      <Header />
      
      {/* Hero Section */}
      {featuredContent && (
        <div className="relative h-screen">
          <div className="absolute inset-0">
            <img
              src={getImageUrl(featuredContent.backdrop)}
              alt={featuredContent.title}
              className="w-full h-full object-cover"
              onError={(e) => {
                e.currentTarget.src = '/images/placeholder-backdrop.jpg';
              }}
            />
            <div className="absolute inset-0 bg-gradient-to-r from-black via-black/50 to-transparent" />
            <div className="absolute inset-0 bg-gradient-to-t from-black via-transparent to-transparent" />
          </div>
          
          <div className="relative z-10 flex items-center h-full">
            <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
              <div className="max-w-2xl">
                <h1 className="text-white text-4xl md:text-6xl font-bold mb-4">
                  {featuredContent.title}
                </h1>
                
                <div className="flex items-center space-x-4 mb-4">
                  {featuredContent.averageRating && (
                    <div className="flex items-center space-x-1">
                      <Star className="w-5 h-5 text-yellow-400 fill-current" />
                      <span className="text-white">{featuredContent.averageRating.toFixed(1)}</span>
                    </div>
                  )}
                  
                  <span className="text-gray-300">
                    {featuredContent.releaseDate ? new Date(featuredContent.releaseDate).getFullYear() : ''}
                  </span>
                  
                  {featuredContent.duration && (
                    <div className="flex items-center space-x-1">
                      <Clock className="w-4 h-4 text-gray-400" />
                      <span className="text-gray-300">{formatDuration(featuredContent.duration)}</span>
                    </div>
                  )}
                  
                  <span className="bg-gray-700 text-white px-2 py-1 rounded text-sm">
                    {featuredContent.maturityRating}
                  </span>
                </div>
                
                <p className="text-gray-300 text-lg mb-8 leading-relaxed">
                  {featuredContent.synopsis || featuredContent.description}
                </p>
                
                <div className="flex space-x-4">
                  <Button
                    size="lg"
                    onClick={() => handlePlayContent(featuredContent)}
                    leftIcon={<Play className="w-5 h-5" />}
                  >
                    Play Now
                  </Button>
                  
                  <Button
                    variant="outline"
                    size="lg"
                    onClick={() => handleContentDetails(featuredContent)}
                    leftIcon={<Info className="w-5 h-5" />}
                  >
                    More Info
                  </Button>
                  
                  <Button
                    variant="ghost"
                    size="lg"
                    leftIcon={<Plus className="w-5 h-5" />}
                  >
                    My List
                  </Button>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Content Sections */}
      <div className="relative z-20 -mt-32 pb-20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          {continueWatching.length > 0 && (
            <ContentRow title="Continue Watching" items={continueWatching} />
          )}
          
          <ContentRow 
            title="Recommended for You" 
            items={recommendations.map(r => r.content)} 
            loading={isLoading}
          />
          
          <ContentRow title="Popular Now" items={popularContent} loading={isLoading} />
          <ContentRow title="Recently Added" items={recentContent} loading={isLoading} />
        </div>
      </div>
    </div>
  );
}
