import { Injectable, Logger, NotFoundException, BadRequestException } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { PrismaService } from '../common/prisma/prisma.service';
import { VideoProcessingService } from './services/video-processing.service';
import { CdnService } from './services/cdn.service';
import { DrmService } from './services/drm.service';
import { AnalyticsService } from './services/analytics.service';

@Injectable()
export class StreamingService {
  private readonly logger = new Logger(StreamingService.name);

  constructor(
    private readonly configService: ConfigService,
    private readonly prisma: PrismaService,
    private readonly videoProcessingService: VideoProcessingService,
    private readonly cdnService: CdnService,
    private readonly drmService: DrmService,
    private readonly analyticsService: AnalyticsService,
  ) {}

  async getStreamingUrl(
    contentId: string,
    quality: string = '720p',
    userId?: string,
    profileId?: string,
  ): Promise<{
    streamUrl: string;
    manifestUrl: string;
    drmToken?: string;
    subtitles: any[];
    audioTracks: any[];
  }> {
    try {
      // Verify content exists and is published
      const content = await this.prisma.content.findUnique({
        where: { id: contentId },
      });

      if (!content || content.status !== 'PUBLISHED') {
        throw new NotFoundException('Content not found or not available');
      }

      // Check user subscription and content access
      if (userId) {
        await this.verifyUserAccess(userId, content);
      }

      // Get processed video URLs
      const videoUrls = await this.videoProcessingService.getProcessedVideoUrls(contentId);
      
      if (!videoUrls || !videoUrls[quality]) {
        throw new BadRequestException(`Quality ${quality} not available for this content`);
      }

      // Generate CDN URLs
      const streamUrl = await this.cdnService.generateStreamingUrl(videoUrls[quality]);
      const manifestUrl = await this.cdnService.generateManifestUrl(contentId);

      // Generate DRM token if content is protected
      let drmToken;
      if (content.videoUrl && await this.drmService.isContentProtected(contentId)) {
        drmToken = await this.drmService.generateDrmToken(contentId, userId);
      }

      // Get subtitles and audio tracks
      const subtitles = content.subtitles ? await Promise.all(
        Object.entries(content.subtitles as any).map(async ([lang, subs]) => ({
          language: lang,
          url: await this.cdnService.generateSubtitleUrl(contentId, lang),
          label: this.getLanguageLabel(lang),
        }))
      ) : [];

      const audioTracks = content.audioTracks ? await Promise.all(
        (content.audioTracks as any[]).map(async track => ({
          ...track,
          url: await this.cdnService.generateAudioTrackUrl(contentId, track.id),
        }))
      ) : [];

      // Track streaming analytics
      if (userId && profileId) {
        await this.analyticsService.trackStreamStart(contentId, userId, profileId, quality);
      }

      return {
        streamUrl,
        manifestUrl,
        drmToken,
        subtitles,
        audioTracks,
      };
    } catch (error) {
      this.logger.error(`Error getting streaming URL for content ${contentId}:`, error);
      throw error;
    }
  }

  async getEpisodeStreamingUrl(
    episodeId: string,
    quality: string = '720p',
    userId?: string,
    profileId?: string,
  ): Promise<any> {
    try {
      const episode = await this.prisma.episode.findUnique({
        where: { id: episodeId },
        include: {
          season: {
            include: {
              content: true,
            },
          },
        },
      });

      if (!episode) {
        throw new NotFoundException('Episode not found');
      }

      const content = episode.season.content;
      if (content.status !== 'PUBLISHED') {
        throw new NotFoundException('Content not available');
      }

      // Check user access
      if (userId) {
        await this.verifyUserAccess(userId, content);
      }

      // Get episode video URLs
      const videoUrls = await this.videoProcessingService.getEpisodeVideoUrls(episodeId);
      
      if (!videoUrls || !videoUrls[quality]) {
        throw new BadRequestException(`Quality ${quality} not available for this episode`);
      }

      const streamUrl = await this.cdnService.generateStreamingUrl(videoUrls[quality]);
      const manifestUrl = await this.cdnService.generateEpisodeManifestUrl(episodeId);

      // Generate DRM token
      let drmToken;
      if (episode.videoUrl && await this.drmService.isContentProtected(content.id)) {
        drmToken = await this.drmService.generateDrmToken(content.id, userId);
      }

      // Track analytics
      if (userId && profileId) {
        await this.analyticsService.trackEpisodeStreamStart(episodeId, userId, profileId, quality);
      }

      return {
        streamUrl,
        manifestUrl,
        drmToken,
        episode: {
          id: episode.id,
          title: episode.title,
          number: episode.number,
          duration: episode.duration,
        },
        season: {
          id: episode.season.id,
          number: episode.season.number,
          title: episode.season.title,
        },
        content: {
          id: content.id,
          title: content.title,
          type: content.type,
        },
      };
    } catch (error) {
      this.logger.error(`Error getting episode streaming URL for ${episodeId}:`, error);
      throw error;
    }
  }

  async getAvailableQualities(contentId: string): Promise<string[]> {
    try {
      const videoUrls = await this.videoProcessingService.getProcessedVideoUrls(contentId);
      return Object.keys(videoUrls || {});
    } catch (error) {
      this.logger.error(`Error getting available qualities for ${contentId}:`, error);
      return [];
    }
  }

  async updateWatchProgress(
    contentId: string,
    userId: string,
    profileId: string,
    progress: number,
    episodeId?: string,
  ): Promise<void> {
    try {
      const watchData = {
        userId,
        profileId,
        progress,
        watchedAt: new Date(),
        completed: progress >= 90, // Consider 90% as completed
      };

      if (episodeId) {
        // Update episode watch progress
        const existingRecord = await this.prisma.watchHistory.findFirst({
          where: { userId, profileId, episodeId },
        });

        if (existingRecord) {
          await this.prisma.watchHistory.update({
            where: { id: existingRecord.id },
            data: watchData,
          });
        } else {
          await this.prisma.watchHistory.create({
            data: {
              ...watchData,
              episodeId,
            },
          });
        }
      } else {
        // Update content watch progress
        const existingRecord = await this.prisma.watchHistory.findFirst({
          where: { userId, profileId, contentId },
        });

        if (existingRecord) {
          await this.prisma.watchHistory.update({
            where: { id: existingRecord.id },
            data: watchData,
          });
        } else {
          await this.prisma.watchHistory.create({
            data: {
              ...watchData,
              contentId,
            },
          });
        }
      }

      // Track analytics
      await this.analyticsService.trackWatchProgress(
        contentId,
        userId,
        profileId,
        progress,
        episodeId,
      );
    } catch (error) {
      this.logger.error('Error updating watch progress:', error);
      throw error;
    }
  }

  async getWatchProgress(
    contentId: string,
    userId: string,
    profileId: string,
    episodeId?: string,
  ): Promise<{ progress: number; completed: boolean; lastWatched: Date } | null> {
    try {
      const whereClause = episodeId
        ? { userId, profileId, episodeId }
        : { userId, profileId, contentId };

      const watchHistory = await this.prisma.watchHistory.findFirst({
        where: whereClause,
        orderBy: { watchedAt: 'desc' },
      });

      if (!watchHistory) {
        return null;
      }

      return {
        progress: watchHistory.progress,
        completed: watchHistory.completed,
        lastWatched: watchHistory.watchedAt,
      };
    } catch (error) {
      this.logger.error('Error getting watch progress:', error);
      return null;
    }
  }

  async generateDownloadUrl(
    contentId: string,
    quality: string,
    userId: string,
  ): Promise<{
    downloadUrl: string;
    expiresAt: Date;
    fileSize: number;
  }> {
    try {
      // Verify user subscription allows downloads
      const user = await this.prisma.user.findUnique({
        where: { id: userId },
      });

      if (!user || !this.canUserDownload(user.subscription)) {
        throw new BadRequestException('Download not available for your subscription');
      }

      // Check if content allows downloads
      const content = await this.prisma.content.findUnique({
        where: { id: contentId },
      });

      if (!content || content.status !== 'PUBLISHED') {
        throw new NotFoundException('Content not found');
      }

      // Generate encrypted download URL
      const downloadUrl = await this.drmService.generateEncryptedDownloadUrl(
        contentId,
        quality,
        userId,
      );

      // Set expiration (e.g., 48 hours)
      const expiresAt = new Date();
      expiresAt.setHours(expiresAt.getHours() + 48);

      // Get file size
      const fileSize = await this.videoProcessingService.getVideoFileSize(contentId, quality);

      // Create download record
      await this.prisma.download.create({
        data: {
          userId,
          contentId,
          quality,
          status: 'PENDING',
          expiresAt,
          fileSize: BigInt(fileSize),
        },
      });

      return {
        downloadUrl,
        expiresAt,
        fileSize,
      };
    } catch (error) {
      this.logger.error(`Error generating download URL for ${contentId}:`, error);
      throw error;
    }
  }

  private async verifyUserAccess(userId: string, content: any): Promise<void> {
    const user = await this.prisma.user.findUnique({
      where: { id: userId },
      include: {
        subscriptions: {
          where: {
            status: 'ACTIVE',
            endDate: {
              gt: new Date(),
            },
          },
        },
      },
    });

    if (!user) {
      throw new NotFoundException('User not found');
    }

    // Check if user has active subscription
    if (user.subscription === 'FREE' && content.type !== 'FREE') {
      throw new BadRequestException('Subscription required to access this content');
    }

    // Additional access checks can be added here
    // e.g., geographic restrictions, parental controls, etc.
  }

  private canUserDownload(subscriptionType: string): boolean {
    return ['PREMIUM', 'FAMILY'].includes(subscriptionType);
  }

  private getLanguageLabel(languageCode: string): string {
    const languageLabels: Record<string, string> = {
      'en': 'English',
      'es': 'Spanish',
      'fr': 'French',
      'de': 'German',
      'it': 'Italian',
      'pt': 'Portuguese',
      'ja': 'Japanese',
      'ko': 'Korean',
      'zh': 'Chinese',
      'ar': 'Arabic',
      'ru': 'Russian',
    };

    return languageLabels[languageCode] || languageCode.toUpperCase();
  }
}
