'use client';

import React, { useState } from 'react';
import { useRouter } from 'next/navigation';
import Link from 'next/link';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { Mail, Lock, User, Check } from 'lucide-react';
import { useAuthStore } from '@/store/auth.store';
import { useAppStore } from '@/store/app.store';
import { apiClient } from '@/lib/api';
import Button from '@/components/ui/Button';
import Input from '@/components/ui/Input';

const registerSchema = z.object({
  name: z.string().min(2, 'Name must be at least 2 characters'),
  email: z.string().email('Please enter a valid email address'),
  password: z.string().min(8, 'Password must be at least 8 characters'),
  confirmPassword: z.string(),
  acceptTerms: z.boolean().refine(val => val === true, 'You must accept the terms and conditions'),
}).refine((data) => data.password === data.confirmPassword, {
  message: "Passwords don't match",
  path: ["confirmPassword"],
});

type RegisterForm = z.infer<typeof registerSchema>;

export default function RegisterPage() {
  const router = useRouter();
  const { login } = useAuthStore();
  const { addNotification } = useAppStore();
  const [isLoading, setIsLoading] = useState(false);

  const {
    register,
    handleSubmit,
    formState: { errors },
  } = useForm<RegisterForm>({
    resolver: zodResolver(registerSchema),
  });

  const onSubmit = async (data: RegisterForm) => {
    setIsLoading(true);
    
    try {
      const response = await apiClient.auth.register({
        name: data.name,
        email: data.email,
        password: data.password,
      });
      
      // Store auth token
      localStorage.setItem('auth-token', response.token);
      
      // Update auth store
      login(response.user, response.profiles || []);
      
      addNotification({
        type: 'success',
        title: 'Welcome to HyperFlix!',
        message: `Account created successfully for ${response.user.name}`,
      });

      // Redirect to profile creation
      router.push('/profiles/create');
    } catch (error: any) {
      addNotification({
        type: 'error',
        title: 'Registration Failed',
        message: error.response?.data?.message || 'Failed to create account',
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-black via-gray-900 to-red-900 flex items-center justify-center p-4">
      <div className="w-full max-w-md">
        {/* Logo */}
        <div className="text-center mb-8">
          <div className="w-16 h-16 bg-red-600 rounded-lg flex items-center justify-center mx-auto mb-4">
            <span className="text-white font-bold text-2xl">H</span>
          </div>
          <h1 className="text-white text-3xl font-bold">Join HyperFlix</h1>
          <p className="text-gray-400 mt-2">Create your account to start streaming</p>
        </div>

        {/* Register Form */}
        <div className="bg-black/50 backdrop-blur-md rounded-2xl p-8 border border-gray-800">
          <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
            <Input
              {...register('name')}
              type="text"
              placeholder="Full name"
              leftIcon={<User className="w-4 h-4" />}
              error={errors.name?.message}
              disabled={isLoading}
            />

            <Input
              {...register('email')}
              type="email"
              placeholder="Email address"
              leftIcon={<Mail className="w-4 h-4" />}
              error={errors.email?.message}
              disabled={isLoading}
            />

            <Input
              {...register('password')}
              type="password"
              placeholder="Password"
              leftIcon={<Lock className="w-4 h-4" />}
              showPasswordToggle
              error={errors.password?.message}
              disabled={isLoading}
            />

            <Input
              {...register('confirmPassword')}
              type="password"
              placeholder="Confirm password"
              leftIcon={<Lock className="w-4 h-4" />}
              showPasswordToggle
              error={errors.confirmPassword?.message}
              disabled={isLoading}
            />

            <div className="space-y-4">
              <label className="flex items-start space-x-3">
                <input
                  {...register('acceptTerms')}
                  type="checkbox"
                  className="w-4 h-4 text-red-600 bg-gray-800 border-gray-600 rounded focus:ring-red-500 focus:ring-2 mt-0.5"
                  disabled={isLoading}
                />
                <span className="text-sm text-gray-300 leading-relaxed">
                  I agree to the{' '}
                  <Link href="/terms" className="text-red-400 hover:text-red-300 underline">
                    Terms of Service
                  </Link>{' '}
                  and{' '}
                  <Link href="/privacy" className="text-red-400 hover:text-red-300 underline">
                    Privacy Policy
                  </Link>
                </span>
              </label>
              {errors.acceptTerms && (
                <p className="text-sm text-red-600 dark:text-red-400">
                  {errors.acceptTerms.message}
                </p>
              )}
            </div>

            <Button
              type="submit"
              variant="primary"
              size="lg"
              loading={isLoading}
              className="w-full"
            >
              Create Account
            </Button>
          </form>

          {/* Features */}
          <div className="mt-6 pt-6 border-t border-gray-700">
            <p className="text-gray-400 text-sm mb-4">What you get with HyperFlix:</p>
            <div className="space-y-2">
              {[
                'Unlimited streaming in HD and 4K',
                'AI-powered personalized recommendations',
                'Download content for offline viewing',
                'Multiple user profiles',
                'No ads, no interruptions',
              ].map((feature, index) => (
                <div key={index} className="flex items-center space-x-2">
                  <Check className="w-4 h-4 text-green-500" />
                  <span className="text-gray-300 text-sm">{feature}</span>
                </div>
              ))}
            </div>
          </div>

          {/* Sign In Link */}
          <p className="text-center text-gray-400 mt-6">
            Already have an account?{' '}
            <Link
              href="/auth/login"
              className="text-red-400 hover:text-red-300 transition-colors font-medium"
            >
              Sign in
            </Link>
          </p>
        </div>

        {/* Footer */}
        <div className="text-center mt-8 text-gray-500 text-sm">
          <p>© 2024 HyperFlix. All rights reserved.</p>
        </div>
      </div>
    </div>
  );
}
