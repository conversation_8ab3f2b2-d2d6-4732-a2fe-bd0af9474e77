import { Injectable, Logger } from '@nestjs/common';
import { PrismaService } from '../../common/prisma/prisma.service';
import { AiService } from '../ai.service';
import * as fs from 'fs';
import * as path from 'path';

interface SubtitleSegment {
  start: number;
  end: number;
  text: string;
}

@Injectable()
export class SubtitleService {
  private readonly logger = new Logger(SubtitleService.name);

  constructor(
    private readonly prisma: PrismaService,
    private readonly aiService: AiService,
  ) {}

  async generateSubtitlesFromAudio(
    audioFilePath: string,
    contentId: string,
    language: string = 'en',
  ): Promise<SubtitleSegment[]> {
    try {
      // Read audio file
      const audioBuffer = fs.readFileSync(audioFilePath);

      // Transcribe using Whisper
      const transcription = await this.aiService.transcribeAudio(audioBuffer);

      // For now, create a simple subtitle with the full transcription
      // In a real implementation, you'd need to segment the audio and get timestamps
      const subtitles: SubtitleSegment[] = [
        {
          start: 0,
          end: 10, // This would be calculated based on audio duration
          text: transcription,
        },
      ];

      // Save subtitles to database
      await this.saveSubtitles(contentId, language, subtitles);

      return subtitles;
    } catch (error) {
      this.logger.error('Error generating subtitles from audio:', error);
      throw error;
    }
  }

  async translateSubtitles(
    contentId: string,
    sourceLanguage: string,
    targetLanguage: string,
  ): Promise<SubtitleSegment[]> {
    try {
      // Get existing subtitles
      const content = await this.prisma.content.findUnique({
        where: { id: contentId },
      });

      if (!content || !content.subtitles) {
        throw new Error('No subtitles found for content');
      }

      const sourceSubtitles = (content.subtitles as any)[sourceLanguage];
      if (!sourceSubtitles) {
        throw new Error(`No subtitles found for language: ${sourceLanguage}`);
      }

      // Translate each subtitle segment
      const translatedSubtitles: SubtitleSegment[] = await Promise.all(
        sourceSubtitles.map(async (subtitle: SubtitleSegment) => ({
          start: subtitle.start,
          end: subtitle.end,
          text: await this.aiService.translateText(subtitle.text, targetLanguage),
        })),
      );

      // Save translated subtitles
      await this.saveSubtitles(contentId, targetLanguage, translatedSubtitles);

      return translatedSubtitles;
    } catch (error) {
      this.logger.error('Error translating subtitles:', error);
      throw error;
    }
  }

  async generateSubtitlesFromScript(
    script: string,
    contentId: string,
    language: string = 'en',
    videoDuration: number,
  ): Promise<SubtitleSegment[]> {
    try {
      // Split script into sentences
      const sentences = script.split(/[.!?]+/).filter(s => s.trim().length > 0);
      
      // Calculate timing for each sentence
      const avgTimePerSentence = videoDuration / sentences.length;
      
      const subtitles: SubtitleSegment[] = sentences.map((sentence, index) => ({
        start: index * avgTimePerSentence,
        end: (index + 1) * avgTimePerSentence,
        text: sentence.trim(),
      }));

      // Save subtitles
      await this.saveSubtitles(contentId, language, subtitles);

      return subtitles;
    } catch (error) {
      this.logger.error('Error generating subtitles from script:', error);
      throw error;
    }
  }

  async getSubtitles(contentId: string, language: string): Promise<SubtitleSegment[]> {
    try {
      const content = await this.prisma.content.findUnique({
        where: { id: contentId },
      });

      if (!content || !content.subtitles) {
        return [];
      }

      const subtitles = (content.subtitles as any)[language];
      return subtitles || [];
    } catch (error) {
      this.logger.error('Error getting subtitles:', error);
      throw error;
    }
  }

  async getAvailableLanguages(contentId: string): Promise<string[]> {
    try {
      const content = await this.prisma.content.findUnique({
        where: { id: contentId },
      });

      if (!content || !content.subtitles) {
        return [];
      }

      return Object.keys(content.subtitles as any);
    } catch (error) {
      this.logger.error('Error getting available languages:', error);
      throw error;
    }
  }

  async generateVTTFile(subtitles: SubtitleSegment[]): Promise<string> {
    let vttContent = 'WEBVTT\n\n';

    subtitles.forEach((subtitle, index) => {
      const startTime = this.formatTime(subtitle.start);
      const endTime = this.formatTime(subtitle.end);
      
      vttContent += `${index + 1}\n`;
      vttContent += `${startTime} --> ${endTime}\n`;
      vttContent += `${subtitle.text}\n\n`;
    });

    return vttContent;
  }

  async generateSRTFile(subtitles: SubtitleSegment[]): Promise<string> {
    let srtContent = '';

    subtitles.forEach((subtitle, index) => {
      const startTime = this.formatTime(subtitle.start, true);
      const endTime = this.formatTime(subtitle.end, true);
      
      srtContent += `${index + 1}\n`;
      srtContent += `${startTime} --> ${endTime}\n`;
      srtContent += `${subtitle.text}\n\n`;
    });

    return srtContent;
  }

  async enhanceSubtitlesWithAI(
    subtitles: SubtitleSegment[],
    contentContext: string,
  ): Promise<SubtitleSegment[]> {
    try {
      const enhancedSubtitles = await Promise.all(
        subtitles.map(async (subtitle) => {
          const prompt = `
            Improve the following subtitle text for better readability and accuracy.
            Context: ${contentContext}
            Original text: "${subtitle.text}"
            
            Provide only the improved text, maintaining the original meaning but with better grammar, punctuation, and clarity.
          `;

          const enhancedText = await this.aiService.generateText(prompt, {
            maxTokens: 100,
            temperature: 0.3,
          });

          return {
            ...subtitle,
            text: enhancedText.trim(),
          };
        }),
      );

      return enhancedSubtitles;
    } catch (error) {
      this.logger.error('Error enhancing subtitles with AI:', error);
      return subtitles; // Return original if enhancement fails
    }
  }

  private async saveSubtitles(
    contentId: string,
    language: string,
    subtitles: SubtitleSegment[],
  ): Promise<void> {
    try {
      const content = await this.prisma.content.findUnique({
        where: { id: contentId },
      });

      const existingSubtitles = (content?.subtitles as any) || {};
      existingSubtitles[language] = subtitles;

      await this.prisma.content.update({
        where: { id: contentId },
        data: {
          subtitles: existingSubtitles,
        },
      });
    } catch (error) {
      this.logger.error('Error saving subtitles:', error);
      throw error;
    }
  }

  private formatTime(seconds: number, srtFormat: boolean = false): string {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const secs = Math.floor(seconds % 60);
    const milliseconds = Math.floor((seconds % 1) * 1000);

    const separator = srtFormat ? ',' : '.';
    
    return `${hours.toString().padStart(2, '0')}:${minutes
      .toString()
      .padStart(2, '0')}:${secs.toString().padStart(2, '0')}${separator}${milliseconds
      .toString()
      .padStart(3, '0')}`;
  }
}
