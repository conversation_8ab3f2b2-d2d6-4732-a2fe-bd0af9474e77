import { Injectable, NotFoundException } from '@nestjs/common';
import { PrismaService } from '../common/prisma/prisma.service';
import { Episode, Prisma } from '@prisma/client';

@Injectable()
export class EpisodesService {
  constructor(private readonly prisma: PrismaService) {}

  async create(data: Prisma.EpisodeCreateInput): Promise<Episode> {
    return this.prisma.episode.create({
      data,
      include: {
        season: {
          include: {
            content: true,
          },
        },
      },
    });
  }

  async findAll(seasonId: string): Promise<Episode[]> {
    return this.prisma.episode.findMany({
      where: { seasonId },
      orderBy: { number: 'asc' },
      include: {
        season: {
          include: {
            content: true,
          },
        },
      },
    });
  }

  async findById(id: string): Promise<Episode | null> {
    return this.prisma.episode.findUnique({
      where: { id },
      include: {
        season: {
          include: {
            content: true,
          },
        },
        watchHistory: true,
      },
    });
  }

  async update(id: string, data: Prisma.EpisodeUpdateInput): Promise<Episode> {
    try {
      return await this.prisma.episode.update({
        where: { id },
        data,
        include: {
          season: {
            include: {
              content: true,
            },
          },
        },
      });
    } catch (error) {
      if (error.code === 'P2025') {
        throw new NotFoundException('Episode not found');
      }
      throw error;
    }
  }

  async delete(id: string): Promise<Episode> {
    try {
      return await this.prisma.episode.delete({
        where: { id },
      });
    } catch (error) {
      if (error.code === 'P2025') {
        throw new NotFoundException('Episode not found');
      }
      throw error;
    }
  }

  async getNextEpisode(currentEpisodeId: string): Promise<Episode | null> {
    const currentEpisode = await this.findById(currentEpisodeId);
    if (!currentEpisode) {
      return null;
    }

    // Try to find next episode in same season
    let nextEpisode = await this.prisma.episode.findFirst({
      where: {
        seasonId: currentEpisode.seasonId,
        number: currentEpisode.number + 1,
      },
      include: {
        season: {
          include: {
            content: true,
          },
        },
      },
    });

    // If no next episode in current season, try first episode of next season
    if (!nextEpisode && currentEpisode.season) {
      const nextSeason = await this.prisma.season.findFirst({
        where: {
          contentId: currentEpisode.season.contentId,
          number: currentEpisode.season.number + 1,
        },
      });

      if (nextSeason) {
        nextEpisode = await this.prisma.episode.findFirst({
          where: {
            seasonId: nextSeason.id,
            number: 1,
          },
          include: {
            season: {
              include: {
                content: true,
              },
            },
          },
        });
      }
    }

    return nextEpisode;
  }

  async getPreviousEpisode(currentEpisodeId: string): Promise<Episode | null> {
    const currentEpisode = await this.findById(currentEpisodeId);
    if (!currentEpisode) {
      return null;
    }

    // Try to find previous episode in same season
    let previousEpisode = await this.prisma.episode.findFirst({
      where: {
        seasonId: currentEpisode.seasonId,
        number: currentEpisode.number - 1,
      },
      include: {
        season: {
          include: {
            content: true,
          },
        },
      },
    });

    // If no previous episode in current season, try last episode of previous season
    if (!previousEpisode && currentEpisode.number === 1 && currentEpisode.season) {
      const previousSeason = await this.prisma.season.findFirst({
        where: {
          contentId: currentEpisode.season.contentId,
          number: currentEpisode.season.number - 1,
        },
      });

      if (previousSeason) {
        previousEpisode = await this.prisma.episode.findFirst({
          where: {
            seasonId: previousSeason.id,
          },
          orderBy: { number: 'desc' },
          include: {
            season: {
              include: {
                content: true,
              },
            },
          },
        });
      }
    }

    return previousEpisode;
  }
}
