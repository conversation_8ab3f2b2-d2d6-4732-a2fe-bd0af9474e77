import { ApiProperty } from '@nestjs/swagger';
import { IsString, IsOptional, IsArray, IsNumber, IsEnum } from 'class-validator';
import { ContentType } from '@prisma/client';

export class SearchDto {
  @ApiProperty({
    description: 'Search query',
    example: 'action movies with superheroes',
  })
  @IsString()
  query: string;

  @ApiProperty({
    description: 'Search filters',
    required: false,
  })
  @IsOptional()
  filters?: {
    genres?: string[];
    type?: ContentType;
    maturityRating?: string;
    language?: string;
    releaseYear?: number;
  };

  @ApiProperty({
    description: 'Maximum number of results',
    example: 20,
    required: false,
  })
  @IsOptional()
  @IsNumber()
  limit?: number = 20;
}
