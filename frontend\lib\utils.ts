import { type ClassValue, clsx } from 'clsx';
import { twMerge } from 'tailwind-merge';

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs));
}

// Format duration from minutes to human readable format
export function formatDuration(minutes: number): string {
  if (minutes < 60) {
    return `${minutes}m`;
  }
  
  const hours = Math.floor(minutes / 60);
  const remainingMinutes = minutes % 60;
  
  if (remainingMinutes === 0) {
    return `${hours}h`;
  }
  
  return `${hours}h ${remainingMinutes}m`;
}

// Get image URL with fallback
export function getImageUrl(url?: string, fallback?: string): string {
  if (!url) return fallback || '/images/placeholder.jpg';
  
  // If it's already a full URL, return as is
  if (url.startsWith('http')) return url;
  
  // If it's a relative path, prepend the CDN URL
  const cdnUrl = process.env.NEXT_PUBLIC_CDN_URL || '';
  return `${cdnUrl}${url}`;
}
