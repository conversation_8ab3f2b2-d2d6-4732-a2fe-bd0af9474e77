// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

// User Management
model User {
  id                String    @id @default(cuid())
  email             String    @unique
  username          String?   @unique
  firstName         String?
  lastName          String?
  avatar            String?
  emailVerified     DateTime?
  isActive          Boolean   @default(true)
  role              UserRole  @default(USER)
  subscription      SubscriptionType @default(FREE)
  subscriptionEndsAt DateTime?

  // Authentication
  password          String?
  googleId          String?   @unique
  facebookId        String?   @unique
  appleId           String?   @unique

  // Preferences
  language          String    @default("en")
  country           String?
  timezone          String?
  parentalControls  Json?     // Parental control settings

  // Timestamps
  createdAt         DateTime  @default(now())
  updatedAt         DateTime  @updatedAt
  lastLoginAt       DateTime?

  // Relations
  profiles          Profile[]
  watchHistory      WatchHistory[]
  favorites         Favorite[]
  ratings           Rating[]
  downloads         Download[]
  subscriptions     UserSubscription[]
  sessions          UserSession[]

  @@map("users")
}

model Profile {
  id          String      @id @default(cuid())
  name        String
  avatar      String?
  isKids      Boolean     @default(false)
  isMain      Boolean     @default(false)
  language    String      @default("en")
  maturityRating String   @default("PG-13") // G, PG, PG-13, R, NC-17

  // User relation
  userId      String
  user        User        @relation(fields: [userId], references: [id], onDelete: Cascade)

  // Timestamps
  createdAt   DateTime    @default(now())
  updatedAt   DateTime    @updatedAt

  // Relations
  watchHistory WatchHistory[]
  favorites    Favorite[]
  ratings      Rating[]
  recommendations Recommendation[]

  @@map("profiles")
}

// Content Management
model Content {
  id              String        @id @default(cuid())
  title           String
  description     String?
  synopsis        String?
  type            ContentType   // MOVIE, SERIES, DOCUMENTARY, etc.
  status          ContentStatus @default(DRAFT)

  // Media info
  duration        Int?          // Duration in minutes
  releaseDate     DateTime?
  endDate         DateTime?     // For series
  maturityRating  String        @default("PG-13")
  language        String        @default("en")
  originalLanguage String?

  // Visual assets
  thumbnail       String?
  poster          String?
  backdrop        String?
  trailer         String?

  // Metadata
  genres          String[]      // Array of genre IDs
  tags            String[]
  cast            Json?         // Cast and crew information
  awards          Json?         // Awards and nominations

  // Streaming info
  videoUrl        String?
  videoQuality    String[]      @default(["720p", "1080p"])
  subtitles       Json?         // Available subtitles
  audioTracks     Json?         // Available audio tracks

  // Analytics
  viewCount       Int           @default(0)
  averageRating   Float?
  totalRatings    Int           @default(0)

  // Timestamps
  createdAt       DateTime      @default(now())
  updatedAt       DateTime      @updatedAt
  publishedAt     DateTime?

  // Relations
  seasons         Season[]
  watchHistory    WatchHistory[]
  favorites       Favorite[]
  ratings         Rating[]
  downloads       Download[]
  recommendations Recommendation[]

  @@map("content")
}

model Season {
  id          String    @id @default(cuid())
  number      Int
  title       String?
  description String?
  poster      String?
  releaseDate DateTime?

  // Content relation
  contentId   String
  content     Content   @relation(fields: [contentId], references: [id], onDelete: Cascade)

  // Timestamps
  createdAt   DateTime  @default(now())
  updatedAt   DateTime  @updatedAt

  // Relations
  episodes    Episode[]

  @@unique([contentId, number])
  @@map("seasons")
}

model Episode {
  id          String    @id @default(cuid())
  number      Int
  title       String
  description String?
  duration    Int       // Duration in minutes
  thumbnail   String?
  videoUrl    String?
  releaseDate DateTime?

  // Season relation
  seasonId    String
  season      Season    @relation(fields: [seasonId], references: [id], onDelete: Cascade)

  // Timestamps
  createdAt   DateTime  @default(now())
  updatedAt   DateTime  @updatedAt

  // Relations
  watchHistory WatchHistory[]

  @@unique([seasonId, number])
  @@map("episodes")
}

// User Interactions
model WatchHistory {
  id          String    @id @default(cuid())
  watchedAt   DateTime  @default(now())
  progress    Float     @default(0) // Progress percentage (0-100)
  completed   Boolean   @default(false)

  // User and Profile relations
  userId      String
  user        User      @relation(fields: [userId], references: [id], onDelete: Cascade)
  profileId   String
  profile     Profile   @relation(fields: [profileId], references: [id], onDelete: Cascade)

  // Content relations
  contentId   String?
  content     Content?  @relation(fields: [contentId], references: [id], onDelete: Cascade)
  episodeId   String?
  episode     Episode?  @relation(fields: [episodeId], references: [id], onDelete: Cascade)

  @@unique([userId, profileId, contentId])
  @@unique([userId, profileId, episodeId])
  @@map("watch_history")
}

model Favorite {
  id        String   @id @default(cuid())
  addedAt   DateTime @default(now())

  // User and Profile relations
  userId    String
  user      User     @relation(fields: [userId], references: [id], onDelete: Cascade)
  profileId String
  profile   Profile  @relation(fields: [profileId], references: [id], onDelete: Cascade)

  // Content relation
  contentId String
  content   Content  @relation(fields: [contentId], references: [id], onDelete: Cascade)

  @@unique([userId, profileId, contentId])
  @@map("favorites")
}

model Rating {
  id        String   @id @default(cuid())
  rating    Float    // 1-5 stars
  review    String?
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // User and Profile relations
  userId    String
  user      User     @relation(fields: [userId], references: [id], onDelete: Cascade)
  profileId String
  profile   Profile  @relation(fields: [profileId], references: [id], onDelete: Cascade)

  // Content relation
  contentId String
  content   Content  @relation(fields: [contentId], references: [id], onDelete: Cascade)

  @@unique([userId, profileId, contentId])
  @@map("ratings")
}

// AI and Recommendations
model Recommendation {
  id          String              @id @default(cuid())
  type        RecommendationType  // TRENDING, PERSONALIZED, SIMILAR, etc.
  score       Float               // Recommendation confidence score
  reason      String?             // Why this was recommended

  // Profile relation
  profileId   String
  profile     Profile             @relation(fields: [profileId], references: [id], onDelete: Cascade)

  // Content relation
  contentId   String
  content     Content             @relation(fields: [contentId], references: [id], onDelete: Cascade)

  // Timestamps
  createdAt   DateTime            @default(now())
  expiresAt   DateTime?

  @@unique([profileId, contentId, type])
  @@map("recommendations")
}

// Downloads and Offline Content
model Download {
  id          String        @id @default(cuid())
  status      DownloadStatus @default(PENDING)
  quality     String        @default("720p")
  fileSize    BigInt?       // File size in bytes
  filePath    String?       // Local file path
  expiresAt   DateTime?     // When download expires

  // User relation
  userId      String
  user        User          @relation(fields: [userId], references: [id], onDelete: Cascade)

  // Content relation
  contentId   String
  content     Content       @relation(fields: [contentId], references: [id], onDelete: Cascade)

  // Timestamps
  createdAt   DateTime      @default(now())
  updatedAt   DateTime      @updatedAt
  downloadedAt DateTime?

  @@unique([userId, contentId])
  @@map("downloads")
}

// Subscriptions and Billing
model UserSubscription {
  id              String            @id @default(cuid())
  type            SubscriptionType
  status          SubscriptionStatus @default(ACTIVE)
  startDate       DateTime          @default(now())
  endDate         DateTime?
  autoRenew       Boolean           @default(true)

  // Billing
  amount          Decimal           @db.Decimal(10, 2)
  currency        String            @default("USD")
  billingCycle    BillingCycle      @default(MONTHLY)

  // Payment info
  paymentMethod   String?           // stripe_card, paypal, etc.
  paymentId       String?           // External payment ID

  // User relation
  userId          String
  user            User              @relation(fields: [userId], references: [id], onDelete: Cascade)

  // Timestamps
  createdAt       DateTime          @default(now())
  updatedAt       DateTime          @updatedAt

  @@map("user_subscriptions")
}

// Authentication and Sessions
model UserSession {
  id          String    @id @default(cuid())
  sessionToken String   @unique
  deviceInfo  Json?     // Device and browser information
  ipAddress   String?
  location    String?   // Geolocation
  isActive    Boolean   @default(true)

  // User relation
  userId      String
  user        User      @relation(fields: [userId], references: [id], onDelete: Cascade)

  // Timestamps
  createdAt   DateTime  @default(now())
  updatedAt   DateTime  @updatedAt
  expiresAt   DateTime
  lastUsedAt  DateTime  @default(now())

  @@map("user_sessions")
}

// Analytics and Metrics
model ViewAnalytics {
  id          String    @id @default(cuid())

  // Content info
  contentId   String?
  episodeId   String?

  // User info (optional for anonymous tracking)
  userId      String?
  profileId   String?
  sessionId   String?

  // View details
  startTime   DateTime  @default(now())
  endTime     DateTime?
  duration    Int?      // Actual watch duration in seconds
  progress    Float?    // How far they watched (0-100%)
  quality     String?   // Video quality watched

  // Device and location
  deviceType  String?   // mobile, desktop, tv, etc.
  platform    String?   // web, ios, android, etc.
  country     String?
  region      String?

  // Engagement metrics
  paused      Int       @default(0) // Number of pauses
  seeked      Int       @default(0) // Number of seeks
  buffering   Int       @default(0) // Buffering events

  @@map("view_analytics")
}

// Live Streaming
model LiveStream {
  id          String          @id @default(cuid())
  title       String
  description String?
  status      LiveStreamStatus @default(SCHEDULED)

  // Stream details
  streamKey   String          @unique
  streamUrl   String?
  thumbnailUrl String?

  // Scheduling
  scheduledAt DateTime?
  startedAt   DateTime?
  endedAt     DateTime?

  // Analytics
  viewerCount Int             @default(0)
  peakViewers Int             @default(0)

  // Content relation (optional)
  contentId   String?

  // Timestamps
  createdAt   DateTime        @default(now())
  updatedAt   DateTime        @updatedAt

  @@map("live_streams")
}

// Enums
enum UserRole {
  USER
  ADMIN
  MODERATOR
  CONTENT_MANAGER
}

enum SubscriptionType {
  FREE
  BASIC
  PREMIUM
  FAMILY
}

enum SubscriptionStatus {
  ACTIVE
  INACTIVE
  CANCELLED
  EXPIRED
  PENDING
}

enum BillingCycle {
  MONTHLY
  YEARLY
  LIFETIME
}

enum ContentType {
  MOVIE
  SERIES
  DOCUMENTARY
  SHORT
  LIVE
  SPORTS
  NEWS
}

enum ContentStatus {
  DRAFT
  PUBLISHED
  ARCHIVED
  DELETED
}

enum RecommendationType {
  TRENDING
  PERSONALIZED
  SIMILAR
  BECAUSE_YOU_WATCHED
  NEW_RELEASES
  CONTINUE_WATCHING
}

enum DownloadStatus {
  PENDING
  DOWNLOADING
  COMPLETED
  FAILED
  EXPIRED
}

enum LiveStreamStatus {
  SCHEDULED
  LIVE
  ENDED
  CANCELLED
}
