{"name": "hyperflix-backend", "version": "1.0.0", "description": "HyperFlix Backend API with NestJS", "main": "dist/main.js", "scripts": {"build": "nest build", "format": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\"", "start": "node dist/main", "start:dev": "nest start --watch", "start:debug": "nest start --debug --watch", "start:prod": "node dist/main", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix", "test": "jest", "test:watch": "jest --watch", "test:cov": "jest --coverage", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "jest --config ./test/jest-e2e.json", "prisma:generate": "prisma generate", "prisma:migrate": "prisma migrate dev", "prisma:deploy": "prisma migrate deploy", "prisma:seed": "ts-node prisma/seed.ts"}, "dependencies": {"@nestjs/common": "^10.0.0", "@nestjs/core": "^10.0.0", "@nestjs/platform-express": "^10.0.0", "@nestjs/config": "^3.1.1", "@nestjs/jwt": "^10.2.0", "@nestjs/passport": "^10.0.2", "@nestjs/swagger": "^7.1.17", "@nestjs/throttler": "^5.0.1", "@nestjs/cache-manager": "^2.1.1", "@nestjs/schedule": "^4.0.0", "@prisma/client": "^5.7.1", "prisma": "^5.7.1", "passport": "^0.7.0", "passport-jwt": "^4.0.1", "passport-local": "^1.0.0", "passport-google-oauth20": "^2.0.0", "bcryptjs": "^2.4.3", "class-validator": "^0.14.0", "class-transformer": "^0.5.1", "cache-manager": "^5.3.2", "cache-manager-redis-yet": "^4.1.2", "redis": "^4.6.12", "openai": "^4.20.1", "aws-sdk": "^2.1509.0", "@aws-sdk/client-s3": "^3.470.0", "@aws-sdk/s3-request-presigner": "^3.470.0", "multer": "^1.4.5-lts.1", "multer-s3": "^3.0.1", "helmet": "^7.1.0", "compression": "^1.7.4", "express-rate-limit": "^7.1.5", "cookie-parser": "^1.4.6", "cors": "^2.8.5", "reflect-metadata": "^0.1.13", "rxjs": "^7.8.1", "uuid": "^9.0.1", "moment": "^2.29.4", "lodash": "^4.17.21"}, "devDependencies": {"@nestjs/cli": "^10.0.0", "@nestjs/schematics": "^10.0.0", "@nestjs/testing": "^10.0.0", "@types/express": "^4.17.17", "@types/jest": "^29.5.2", "@types/node": "^20.3.1", "@types/supertest": "^2.0.12", "@types/bcryptjs": "^2.4.6", "@types/passport-jwt": "^3.0.13", "@types/passport-local": "^1.0.38", "@types/passport-google-oauth20": "^2.0.14", "@types/multer": "^1.4.11", "@types/cookie-parser": "^1.4.6", "@types/cors": "^2.8.17", "@types/uuid": "^9.0.7", "@types/moment": "^2.13.0", "@types/lodash": "^4.14.202", "@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0", "eslint": "^8.42.0", "eslint-config-prettier": "^9.0.0", "eslint-plugin-prettier": "^5.0.0", "jest": "^29.5.0", "prettier": "^3.0.0", "source-map-support": "^0.5.21", "supertest": "^6.3.3", "ts-jest": "^29.1.0", "ts-loader": "^9.4.3", "ts-node": "^10.9.1", "tsconfig-paths": "^4.2.0", "typescript": "^5.1.3"}, "jest": {"moduleFileExtensions": ["js", "json", "ts"], "rootDir": "src", "testRegex": ".*\\.spec\\.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "collectCoverageFrom": ["**/*.(t|j)s"], "coverageDirectory": "../coverage", "testEnvironment": "node"}, "keywords": ["<PERSON><PERSON><PERSON>", "streaming", "video", "ai", "typescript"], "author": "HyperFlix Team", "license": "MIT"}