import { Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';

// Controllers
import { ContentController } from './content.controller';
import { SeasonsController } from './seasons.controller';
import { EpisodesController } from './episodes.controller';

// Services
import { ContentService } from './content.service';
import { SeasonsService } from './seasons.service';
import { EpisodesService } from './episodes.service';
import { FileUploadService } from './services/file-upload.service';
import { MetadataService } from './services/metadata.service';

@Module({
  imports: [ConfigModule],
  controllers: [
    ContentController,
    SeasonsController,
    EpisodesController,
  ],
  providers: [
    ContentService,
    SeasonsService,
    EpisodesService,
    FileUploadService,
    MetadataService,
  ],
  exports: [
    ContentService,
    SeasonsService,
    EpisodesService,
    FileUploadService,
    MetadataService,
  ],
})
export class ContentModule {}
