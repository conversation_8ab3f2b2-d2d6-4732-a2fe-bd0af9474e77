// User and Authentication Types
export interface User {
  id: string;
  email: string;
  name: string;
  avatar?: string;
  subscription: 'FREE' | 'BASIC' | 'PREMIUM' | 'FAMILY';
  role: 'USER' | 'ADMIN' | 'CONTENT_MANAGER';
  createdAt: string;
  updatedAt: string;
}

export interface Profile {
  id: string;
  name: string;
  avatar?: string;
  isKid: boolean;
  language: string;
  maturityLevel: string;
  userId: string;
  createdAt: string;
  updatedAt: string;
}

export interface AuthState {
  user: User | null;
  profiles: Profile[];
  currentProfile: Profile | null;
  isLoading: boolean;
  isAuthenticated: boolean;
}

// Content Types
export type ContentType = 'MOVIE' | 'SERIES' | 'DOCUMENTARY' | 'SHORT';
export type ContentStatus = 'DRAFT' | 'PUBLISHED' | 'ARCHIVED';

export interface Content {
  id: string;
  title: string;
  description?: string;
  synopsis?: string;
  type: ContentType;
  duration?: number;
  releaseDate?: string;
  maturityRating?: string;
  language?: string;
  originalLanguage?: string;
  thumbnail?: string;
  poster?: string;
  backdrop?: string;
  trailer?: string;
  videoUrl?: string;
  genres: string[];
  tags: string[];
  cast?: any[];
  crew?: any[];
  videoQuality: string[];
  subtitles?: any;
  audioTracks?: any[];
  viewCount: number;
  averageRating?: number;
  totalRatings: number;
  status: ContentStatus;
  publishedAt?: string;
  createdAt: string;
  updatedAt: string;
  seasons?: Season[];
}

export interface Season {
  id: string;
  number: number;
  title: string;
  description?: string;
  poster?: string;
  releaseDate?: string;
  contentId: string;
  episodes: Episode[];
  createdAt: string;
  updatedAt: string;
}

export interface Episode {
  id: string;
  number: number;
  title: string;
  description?: string;
  duration?: number;
  thumbnail?: string;
  videoUrl?: string;
  releaseDate?: string;
  seasonId: string;
  createdAt: string;
  updatedAt: string;
}

// Streaming Types
export interface StreamingData {
  streamUrl: string;
  manifestUrl: string;
  drmToken?: string;
  subtitles: SubtitleTrack[];
  audioTracks: AudioTrack[];
}

export interface SubtitleTrack {
  language: string;
  url: string;
  label: string;
}

export interface AudioTrack {
  id: string;
  language: string;
  label: string;
  codec: string;
  channels: number;
  url: string;
}

export interface WatchProgress {
  progress: number;
  completed: boolean;
  lastWatched: Date;
}

// Recommendation Types
export type RecommendationType = 'PERSONALIZED' | 'SIMILAR' | 'TRENDING' | 'CONTINUE_WATCHING';

export interface Recommendation {
  content: Content;
  reason: string;
  score: number;
  type: RecommendationType;
  explanation?: string;
  confidence?: number;
}

// Search Types
export interface SearchResult {
  content: Content;
  relevanceScore: number;
  matchedFields: string[];
  aiSummary?: string;
}

export interface SearchFilters {
  type?: ContentType;
  genres?: string[];
  maturityRating?: string;
  language?: string;
  releaseYear?: number;
}

// UI State Types
export interface AppState {
  theme: 'light' | 'dark';
  language: string;
  isLoading: boolean;
  error: string | null;
  notifications: Notification[];
}

export interface Notification {
  id: string;
  type: 'success' | 'error' | 'warning' | 'info';
  title: string;
  message: string;
  duration?: number;
  createdAt: Date;
}

// Video Player Types
export interface VideoPlayerState {
  isPlaying: boolean;
  currentTime: number;
  duration: number;
  volume: number;
  isMuted: boolean;
  isFullscreen: boolean;
  quality: string;
  subtitleTrack?: string;
  audioTrack?: string;
  playbackRate: number;
}

export interface VideoPlayerOptions {
  autoplay?: boolean;
  controls?: boolean;
  fluid?: boolean;
  responsive?: boolean;
  aspectRatio?: string;
  sources: VideoSource[];
  tracks?: VideoTrack[];
}

export interface VideoSource {
  src: string;
  type: string;
  label?: string;
}

export interface VideoTrack {
  kind: 'subtitles' | 'captions' | 'chapters';
  src: string;
  srclang: string;
  label: string;
  default?: boolean;
}

// API Response Types
export interface ApiResponse<T> {
  data: T;
  message?: string;
  success: boolean;
}

export interface PaginatedResponse<T> {
  data: T[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    pages: number;
  };
}

// Download Types
export interface Download {
  id: string;
  contentId: string;
  quality: string;
  status: 'PENDING' | 'DOWNLOADING' | 'COMPLETED' | 'FAILED';
  progress: number;
  fileSize: number;
  downloadedAt?: string;
  expiresAt: string;
}

// Analytics Types
export interface ViewAnalytics {
  contentId: string;
  userId: string;
  profileId: string;
  sessionId: string;
  startTime: Date;
  endTime?: Date;
  progress: number;
  duration?: number;
  quality: string;
  deviceType: string;
  platform: string;
  country?: string;
  region?: string;
}

// Form Types
export interface LoginForm {
  email: string;
  password: string;
  rememberMe?: boolean;
}

export interface RegisterForm {
  name: string;
  email: string;
  password: string;
  confirmPassword: string;
  acceptTerms: boolean;
}

export interface ProfileForm {
  name: string;
  avatar?: string;
  isKid: boolean;
  language: string;
  maturityLevel: string;
}

// Component Props Types
export interface BaseComponentProps {
  className?: string;
  children?: React.ReactNode;
}

export interface ContentCardProps extends BaseComponentProps {
  content: Content;
  size?: 'small' | 'medium' | 'large';
  showProgress?: boolean;
  onClick?: (content: Content) => void;
}

export interface CarouselProps extends BaseComponentProps {
  title: string;
  items: Content[];
  loading?: boolean;
  onItemClick?: (content: Content) => void;
  onSeeAll?: () => void;
}

// Error Types
export interface ApiError {
  message: string;
  statusCode: number;
  error: string;
  timestamp: string;
  path: string;
}
