import { Injectable, NotFoundException, BadRequestException } from '@nestjs/common';
import { PrismaService } from '../common/prisma/prisma.service';
import { Content, ContentStatus, ContentType, Prisma } from '@prisma/client';

@Injectable()
export class ContentService {
  constructor(private readonly prisma: PrismaService) {}

  async create(data: Prisma.ContentCreateInput): Promise<Content> {
    try {
      return await this.prisma.content.create({
        data,
        include: {
          seasons: {
            include: {
              episodes: true,
            },
          },
        },
      });
    } catch (error) {
      throw new BadRequestException('Failed to create content');
    }
  }

  async findAll(params: {
    skip?: number;
    take?: number;
    where?: Prisma.ContentWhereInput;
    orderBy?: Prisma.ContentOrderByWithRelationInput;
    include?: Prisma.ContentInclude;
  }): Promise<Content[]> {
    const { skip, take, where, orderBy, include } = params;
    
    return this.prisma.content.findMany({
      skip,
      take,
      where,
      orderBy,
      include: include || {
        seasons: {
          include: {
            episodes: true,
          },
        },
      },
    });
  }

  async findById(id: string, include?: Prisma.ContentInclude): Promise<Content | null> {
    return this.prisma.content.findUnique({
      where: { id },
      include: include || {
        seasons: {
          include: {
            episodes: true,
          },
        },
        watchHistory: true,
        favorites: true,
        ratings: true,
      },
    });
  }

  async findBySlug(slug: string): Promise<Content | null> {
    // Assuming we add a slug field to the Content model
    return this.prisma.content.findFirst({
      where: {
        title: {
          contains: slug.replace(/-/g, ' '),
          mode: 'insensitive',
        },
      },
      include: {
        seasons: {
          include: {
            episodes: true,
          },
        },
      },
    });
  }

  async update(id: string, data: Prisma.ContentUpdateInput): Promise<Content> {
    try {
      return await this.prisma.content.update({
        where: { id },
        data,
        include: {
          seasons: {
            include: {
              episodes: true,
            },
          },
        },
      });
    } catch (error) {
      if (error.code === 'P2025') {
        throw new NotFoundException('Content not found');
      }
      throw new BadRequestException('Failed to update content');
    }
  }

  async delete(id: string): Promise<Content> {
    try {
      return await this.prisma.content.delete({
        where: { id },
      });
    } catch (error) {
      if (error.code === 'P2025') {
        throw new NotFoundException('Content not found');
      }
      throw new BadRequestException('Failed to delete content');
    }
  }

  async publish(id: string): Promise<Content> {
    return this.update(id, {
      status: ContentStatus.PUBLISHED,
      publishedAt: new Date(),
    });
  }

  async unpublish(id: string): Promise<Content> {
    return this.update(id, {
      status: ContentStatus.DRAFT,
      publishedAt: null,
    });
  }

  async archive(id: string): Promise<Content> {
    return this.update(id, {
      status: ContentStatus.ARCHIVED,
    });
  }

  async incrementViewCount(id: string): Promise<void> {
    await this.prisma.content.update({
      where: { id },
      data: {
        viewCount: {
          increment: 1,
        },
      },
    });
  }

  async updateRating(id: string): Promise<void> {
    const ratings = await this.prisma.rating.findMany({
      where: { contentId: id },
    });

    if (ratings.length === 0) {
      return;
    }

    const averageRating = ratings.reduce((sum, rating) => sum + rating.rating, 0) / ratings.length;

    await this.prisma.content.update({
      where: { id },
      data: {
        averageRating,
        totalRatings: ratings.length,
      },
    });
  }

  async getContentByGenre(genre: string, limit: number = 20): Promise<Content[]> {
    return this.findAll({
      where: {
        status: ContentStatus.PUBLISHED,
        genres: {
          has: genre,
        },
      },
      take: limit,
      orderBy: {
        averageRating: 'desc',
      },
    });
  }

  async getContentByType(type: ContentType, limit: number = 20): Promise<Content[]> {
    return this.findAll({
      where: {
        status: ContentStatus.PUBLISHED,
        type,
      },
      take: limit,
      orderBy: {
        publishedAt: 'desc',
      },
    });
  }

  async getPopularContent(limit: number = 20): Promise<Content[]> {
    return this.findAll({
      where: {
        status: ContentStatus.PUBLISHED,
      },
      take: limit,
      orderBy: {
        viewCount: 'desc',
      },
    });
  }

  async getRecentlyAdded(limit: number = 20): Promise<Content[]> {
    return this.findAll({
      where: {
        status: ContentStatus.PUBLISHED,
      },
      take: limit,
      orderBy: {
        publishedAt: 'desc',
      },
    });
  }

  async getTopRated(limit: number = 20): Promise<Content[]> {
    return this.findAll({
      where: {
        status: ContentStatus.PUBLISHED,
        totalRatings: {
          gte: 10, // Minimum number of ratings
        },
      },
      take: limit,
      orderBy: {
        averageRating: 'desc',
      },
    });
  }

  async searchContent(query: string, filters?: {
    type?: ContentType;
    genres?: string[];
    maturityRating?: string;
    language?: string;
  }): Promise<Content[]> {
    const whereConditions: Prisma.ContentWhereInput = {
      AND: [
        { status: ContentStatus.PUBLISHED },
        {
          OR: [
            {
              title: {
                contains: query,
                mode: 'insensitive',
              },
            },
            {
              description: {
                contains: query,
                mode: 'insensitive',
              },
            },
            {
              tags: {
                hasSome: query.split(' '),
              },
            },
          ],
        },
      ],
    };

    if (filters) {
      const andConditions = whereConditions.AND as any[];
      if (filters.type) {
        andConditions.push({ type: filters.type });
      }
      if (filters.genres?.length) {
        andConditions.push({
          genres: {
            hasSome: filters.genres,
          },
        });
      }
      if (filters.maturityRating) {
        andConditions.push({ maturityRating: filters.maturityRating });
      }
      if (filters.language) {
        andConditions.push({ language: filters.language });
      }
    }

    return this.findAll({
      where: whereConditions,
      orderBy: {
        averageRating: 'desc',
      },
    });
  }

  async getContentStats(id: string): Promise<{
    totalViews: number;
    averageRating: number;
    totalRatings: number;
    totalFavorites: number;
    completionRate: number;
  }> {
    const content = await this.findById(id);
    if (!content) {
      throw new NotFoundException('Content not found');
    }

    const [favorites, watchHistory] = await Promise.all([
      this.prisma.favorite.count({
        where: { contentId: id },
      }),
      this.prisma.watchHistory.findMany({
        where: { contentId: id },
      }),
    ]);

    const completedWatches = watchHistory.filter(wh => wh.completed).length;
    const completionRate = watchHistory.length > 0 ? (completedWatches / watchHistory.length) * 100 : 0;

    return {
      totalViews: content.viewCount,
      averageRating: content.averageRating || 0,
      totalRatings: content.totalRatings,
      totalFavorites: favorites,
      completionRate,
    };
  }

  async count(where?: Prisma.ContentWhereInput): Promise<number> {
    return this.prisma.content.count({ where });
  }

  async getGenres(): Promise<string[]> {
    const contents = await this.prisma.content.findMany({
      where: { status: ContentStatus.PUBLISHED },
      select: { genres: true },
    });

    const allGenres = contents.flatMap(content => content.genres);
    return [...new Set(allGenres)].sort();
  }

  async getLanguages(): Promise<string[]> {
    const languages = await this.prisma.content.findMany({
      where: { status: ContentStatus.PUBLISHED },
      select: { language: true },
      distinct: ['language'],
    });

    return languages.map(l => l.language).filter(Boolean).sort();
  }
}
