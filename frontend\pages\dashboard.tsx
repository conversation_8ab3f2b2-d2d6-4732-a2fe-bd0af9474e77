import React, { useEffect, useState } from 'react';
import Head from 'next/head';
import { useRouter } from 'next/router';
import { Play, Plus, Info, Star, Clock, Search, Bell, User, Menu, X, LogOut, Settings } from 'lucide-react';
import { useAuthStore } from '@/store/auth.store';
import { useAppStore } from '@/store/app.store';
import { Content } from '@/types';
import Button from '@/components/ui/Button';
import { formatDuration, cn } from '@/lib/utils';

// Mock data for demo
const mockContent: Content[] = [
  {
    id: '1',
    title: 'The Matrix',
    description: 'A computer programmer discovers that reality as he knows it is a simulation.',
    synopsis: '<PERSON>, a computer programmer, is led to fight an underground war against powerful computers who have constructed his entire reality with a system called the Matrix.',
    type: 'MOVIE',
    duration: 136,
    releaseDate: '1999-03-31',
    maturityRating: 'R',
    language: 'en',
    originalLanguage: 'en',
    thumbnail: '/images/matrix-thumb.jpg',
    poster: '/images/matrix-poster.jpg',
    backdrop: '/images/matrix-backdrop.jpg',
    trailer: '/videos/matrix-trailer.mp4',
    videoUrl: '/videos/matrix.mp4',
    genres: ['Action', 'Sci-Fi'],
    tags: ['cyberpunk', 'virtual reality', 'philosophy'],
    cast: [],
    crew: [],
    videoQuality: ['720p', '1080p', '4K'],
    subtitles: {},
    audioTracks: [],
    viewCount: 1500000,
    averageRating: 8.7,
    totalRatings: 250000,
    status: 'PUBLISHED',
    publishedAt: '1999-03-31',
    createdAt: '2024-01-01',
    updatedAt: '2024-01-01',
  },
  {
    id: '2',
    title: 'Inception',
    description: 'A thief who steals corporate secrets through dream-sharing technology.',
    synopsis: 'Dom Cobb is a skilled thief, the absolute best in the dangerous art of extraction, stealing valuable secrets from deep within the subconscious during the dream state.',
    type: 'MOVIE',
    duration: 148,
    releaseDate: '2010-07-16',
    maturityRating: 'PG-13',
    language: 'en',
    originalLanguage: 'en',
    thumbnail: '/images/inception-thumb.jpg',
    poster: '/images/inception-poster.jpg',
    backdrop: '/images/inception-backdrop.jpg',
    trailer: '/videos/inception-trailer.mp4',
    videoUrl: '/videos/inception.mp4',
    genres: ['Action', 'Sci-Fi', 'Thriller'],
    tags: ['dreams', 'heist', 'mind-bending'],
    cast: [],
    crew: [],
    videoQuality: ['720p', '1080p', '4K'],
    subtitles: {},
    audioTracks: [],
    viewCount: 2000000,
    averageRating: 8.8,
    totalRatings: 300000,
    status: 'PUBLISHED',
    publishedAt: '2010-07-16',
    createdAt: '2024-01-01',
    updatedAt: '2024-01-01',
  },
  {
    id: '3',
    title: 'Interstellar',
    description: 'A team of explorers travel through a wormhole in space in an attempt to ensure humanity\'s survival.',
    synopsis: 'Earth\'s future has been riddled by disasters, famines, and droughts. There is only one way to ensure mankind\'s survival: Interstellar travel.',
    type: 'MOVIE',
    duration: 169,
    releaseDate: '2014-11-07',
    maturityRating: 'PG-13',
    language: 'en',
    originalLanguage: 'en',
    thumbnail: '/images/interstellar-thumb.jpg',
    poster: '/images/interstellar-poster.jpg',
    backdrop: '/images/interstellar-backdrop.jpg',
    trailer: '/videos/interstellar-trailer.mp4',
    videoUrl: '/videos/interstellar.mp4',
    genres: ['Adventure', 'Drama', 'Sci-Fi'],
    tags: ['space', 'time travel', 'emotional'],
    cast: [],
    crew: [],
    videoQuality: ['720p', '1080p', '4K'],
    subtitles: {},
    audioTracks: [],
    viewCount: 1800000,
    averageRating: 8.6,
    totalRatings: 280000,
    status: 'PUBLISHED',
    publishedAt: '2014-11-07',
    createdAt: '2024-01-01',
    updatedAt: '2024-01-01',
  },
];

export default function DashboardPage() {
  const router = useRouter();
  const { isAuthenticated, currentProfile, profiles, logout, switchProfile } = useAuthStore();
  const { addNotification } = useAppStore();
  
  const [featuredContent, setFeaturedContent] = useState<Content | null>(null);
  const [popularContent, setPopularContent] = useState<Content[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [isProfileMenuOpen, setIsProfileMenuOpen] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');

  useEffect(() => {
    if (!isAuthenticated || !currentProfile) {
      router.push('/auth/login');
      return;
    }

    loadDashboardData();
  }, [isAuthenticated, currentProfile, router]);

  const loadDashboardData = async () => {
    try {
      setIsLoading(true);
      
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      setFeaturedContent(mockContent[0]);
      setPopularContent(mockContent);
      
    } catch (error: any) {
      console.error('Error loading dashboard data:', error);
      addNotification({
        type: 'error',
        title: 'Error',
        message: 'Failed to load content. Please try again.',
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handlePlayContent = (content: Content) => {
    addNotification({
      type: 'info',
      title: 'Demo Mode',
      message: `Playing ${content.title} - Video player coming soon!`,
    });
  };

  const handleContentDetails = (content: Content) => {
    addNotification({
      type: 'info',
      title: 'Demo Mode',
      message: `Showing details for ${content.title} - Details page coming soon!`,
    });
  };

  const handleLogout = () => {
    logout();
    router.push('/auth/login');
  };

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    if (searchQuery.trim()) {
      addNotification({
        type: 'info',
        title: 'Demo Mode',
        message: `Searching for "${searchQuery}" - Search functionality coming soon!`,
      });
      setSearchQuery('');
    }
  };

  const navigation = [
    { name: 'Home', href: '/dashboard' },
    { name: 'Movies', href: '/movies' },
    { name: 'TV Shows', href: '/tv-shows' },
    { name: 'My List', href: '/my-list' },
  ];

  const ContentCard = ({ content }: { content: Content }) => (
    <div className="w-40 h-60 flex-shrink-0 group cursor-pointer transition-transform hover:scale-105">
      <div className="relative w-full h-full rounded-lg overflow-hidden bg-gray-800">
        <div className="w-full h-full bg-gradient-to-br from-red-600 to-red-800 flex items-center justify-center">
          <span className="text-white font-bold text-lg">{content.title.charAt(0)}</span>
        </div>
        
        {/* Overlay */}
        <div className="absolute inset-0 bg-black/60 opacity-0 group-hover:opacity-100 transition-opacity flex items-center justify-center">
          <div className="text-center space-y-2">
            <Button
              size="sm"
              onClick={() => handlePlayContent(content)}
              leftIcon={<Play className="w-4 h-4" />}
            >
              Play
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={() => handleContentDetails(content)}
              leftIcon={<Info className="w-4 h-4" />}
            >
              Info
            </Button>
          </div>
        </div>

        {/* Rating */}
        {content.averageRating && (
          <div className="absolute top-2 right-2 bg-black/80 rounded px-2 py-1 flex items-center space-x-1">
            <Star className="w-3 h-3 text-yellow-400 fill-current" />
            <span className="text-white text-xs">{content.averageRating.toFixed(1)}</span>
          </div>
        )}
      </div>
      
      <div className="mt-2">
        <h3 className="text-white text-sm font-medium truncate">{content.title}</h3>
        <p className="text-gray-400 text-xs">{content.releaseDate ? new Date(content.releaseDate).getFullYear() : ''}</p>
      </div>
    </div>
  );

  if (isLoading) {
    return (
      <div>
        <Head>
          <title>Dashboard - HyperFlix</title>
        </Head>
        <div className="min-h-screen bg-black flex items-center justify-center">
          <div className="text-center">
            <div className="w-16 h-16 border-4 border-gray-300 border-t-red-600 rounded-full animate-spin mx-auto mb-4"></div>
            <p className="text-white">Loading your personalized content...</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div>
      <Head>
        <title>Dashboard - HyperFlix</title>
      </Head>

      <div className="min-h-screen bg-black">
        {/* Header */}
        <header className="fixed top-0 left-0 right-0 z-40 bg-black/90 backdrop-blur-md border-b border-gray-800">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="flex items-center justify-between h-16">
              {/* Logo */}
              <div className="flex items-center">
                <div className="flex items-center space-x-2">
                  <div className="w-8 h-8 bg-red-600 rounded-lg flex items-center justify-center">
                    <span className="text-white font-bold text-lg">H</span>
                  </div>
                  <span className="text-white font-bold text-xl hidden sm:block">HyperFlix</span>
                </div>
              </div>

              {/* Desktop Navigation */}
              <nav className="hidden md:flex items-center space-x-8">
                {navigation.map((item) => (
                  <button
                    key={item.name}
                    onClick={() => addNotification({
                      type: 'info',
                      title: 'Demo Mode',
                      message: `${item.name} page coming soon!`,
                    })}
                    className="text-gray-300 hover:text-white transition-colors duration-200"
                  >
                    {item.name}
                  </button>
                ))}
              </nav>

              {/* Search Bar */}
              <div className="hidden lg:flex flex-1 max-w-lg mx-8">
                <form onSubmit={handleSearch} className="w-full">
                  <div className="relative">
                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                    <input
                      type="text"
                      placeholder="Search movies, TV shows..."
                      value={searchQuery}
                      onChange={(e) => setSearchQuery(e.target.value)}
                      className="w-full pl-10 pr-4 py-2 bg-gray-800 border border-gray-700 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-transparent"
                    />
                  </div>
                </form>
              </div>

              {/* Right Side Actions */}
              <div className="flex items-center space-x-4">
                {/* Mobile Search */}
                <button className="lg:hidden text-gray-300 hover:text-white">
                  <Search className="w-5 h-5" />
                </button>

                {/* Notifications */}
                <button className="text-gray-300 hover:text-white relative">
                  <Bell className="w-5 h-5" />
                  <span className="absolute -top-1 -right-1 w-2 h-2 bg-red-600 rounded-full"></span>
                </button>

                {/* Profile Menu */}
                <div className="relative">
                  <button
                    onClick={() => setIsProfileMenuOpen(!isProfileMenuOpen)}
                    className="flex items-center space-x-2 text-gray-300 hover:text-white"
                  >
                    <div className="w-8 h-8 bg-gradient-to-r from-red-500 to-pink-500 rounded-full flex items-center justify-center">
                      <User className="w-4 h-4 text-white" />
                    </div>
                    <span className="hidden sm:block">{currentProfile?.name}</span>
                  </button>

                  {/* Profile Dropdown */}
                  {isProfileMenuOpen && (
                    <div className="absolute right-0 mt-2 w-64 bg-gray-900 rounded-lg shadow-xl border border-gray-700 py-2">
                      {/* Profile Switcher */}
                      <div className="px-4 py-2 border-b border-gray-700">
                        <p className="text-sm text-gray-400 mb-2">Switch Profile</p>
                        <div className="grid grid-cols-2 gap-2">
                          {profiles.map((profile) => (
                            <button
                              key={profile.id}
                              onClick={() => {
                                switchProfile(profile);
                                setIsProfileMenuOpen(false);
                              }}
                              className={cn(
                                'flex items-center space-x-2 p-2 rounded-lg transition-colors',
                                currentProfile?.id === profile.id
                                  ? 'bg-red-600 text-white'
                                  : 'text-gray-300 hover:bg-gray-800'
                              )}
                            >
                              <div className="w-6 h-6 bg-gradient-to-r from-red-500 to-pink-500 rounded-full flex items-center justify-center">
                                <User className="w-3 h-3 text-white" />
                              </div>
                              <span className="text-sm truncate">{profile.name}</span>
                            </button>
                          ))}
                        </div>
                      </div>

                      {/* Menu Items */}
                      <div className="py-2">
                        <button
                          onClick={() => {
                            setIsProfileMenuOpen(false);
                            addNotification({
                              type: 'info',
                              title: 'Demo Mode',
                              message: 'Profile management coming soon!',
                            });
                          }}
                          className="flex items-center space-x-3 px-4 py-2 text-gray-300 hover:bg-gray-800 hover:text-white w-full text-left"
                        >
                          <User className="w-4 h-4" />
                          <span>Manage Profiles</span>
                        </button>
                        
                        <button
                          onClick={() => {
                            setIsProfileMenuOpen(false);
                            addNotification({
                              type: 'info',
                              title: 'Demo Mode',
                              message: 'Settings page coming soon!',
                            });
                          }}
                          className="flex items-center space-x-3 px-4 py-2 text-gray-300 hover:bg-gray-800 hover:text-white w-full text-left"
                        >
                          <Settings className="w-4 h-4" />
                          <span>Settings</span>
                        </button>
                        
                        <button
                          onClick={handleLogout}
                          className="flex items-center space-x-3 px-4 py-2 text-gray-300 hover:bg-gray-800 hover:text-white w-full text-left"
                        >
                          <LogOut className="w-4 h-4" />
                          <span>Sign Out</span>
                        </button>
                      </div>
                    </div>
                  )}
                </div>

                {/* Mobile Menu Button */}
                <button
                  onClick={() => setIsMenuOpen(!isMenuOpen)}
                  className="md:hidden text-gray-300 hover:text-white"
                >
                  {isMenuOpen ? <X className="w-5 h-5" /> : <Menu className="w-5 h-5" />}
                </button>
              </div>
            </div>

            {/* Mobile Menu */}
            {isMenuOpen && (
              <div className="md:hidden border-t border-gray-700 py-4">
                <div className="space-y-2">
                  {navigation.map((item) => (
                    <button
                      key={item.name}
                      onClick={() => {
                        setIsMenuOpen(false);
                        addNotification({
                          type: 'info',
                          title: 'Demo Mode',
                          message: `${item.name} page coming soon!`,
                        });
                      }}
                      className="block px-4 py-2 text-gray-300 hover:text-white hover:bg-gray-800 rounded-lg w-full text-left"
                    >
                      {item.name}
                    </button>
                  ))}
                </div>
                
                {/* Mobile Search */}
                <div className="mt-4 px-4">
                  <form onSubmit={handleSearch}>
                    <div className="relative">
                      <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                      <input
                        type="text"
                        placeholder="Search..."
                        value={searchQuery}
                        onChange={(e) => setSearchQuery(e.target.value)}
                        className="w-full pl-10 pr-4 py-2 bg-gray-800 border border-gray-700 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-red-500"
                      />
                    </div>
                  </form>
                </div>
              </div>
            )}
          </div>
        </header>
        
        {/* Hero Section */}
        {featuredContent && (
          <div className="relative h-screen">
            <div className="absolute inset-0">
              <div className="w-full h-full bg-gradient-to-br from-red-900 via-black to-gray-900"></div>
              <div className="absolute inset-0 bg-gradient-to-r from-black via-black/50 to-transparent" />
              <div className="absolute inset-0 bg-gradient-to-t from-black via-transparent to-transparent" />
            </div>
            
            <div className="relative z-10 flex items-center h-full">
              <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                <div className="max-w-2xl">
                  <h1 className="text-white text-4xl md:text-6xl font-bold mb-4">
                    {featuredContent.title}
                  </h1>
                  
                  <div className="flex items-center space-x-4 mb-4">
                    {featuredContent.averageRating && (
                      <div className="flex items-center space-x-1">
                        <Star className="w-5 h-5 text-yellow-400 fill-current" />
                        <span className="text-white">{featuredContent.averageRating.toFixed(1)}</span>
                      </div>
                    )}
                    
                    <span className="text-gray-300">
                      {featuredContent.releaseDate ? new Date(featuredContent.releaseDate).getFullYear() : ''}
                    </span>
                    
                    {featuredContent.duration && (
                      <div className="flex items-center space-x-1">
                        <Clock className="w-4 h-4 text-gray-400" />
                        <span className="text-gray-300">{formatDuration(featuredContent.duration)}</span>
                      </div>
                    )}
                    
                    <span className="bg-gray-700 text-white px-2 py-1 rounded text-sm">
                      {featuredContent.maturityRating}
                    </span>
                  </div>
                  
                  <p className="text-gray-300 text-lg mb-8 leading-relaxed">
                    {featuredContent.synopsis || featuredContent.description}
                  </p>
                  
                  <div className="flex space-x-4">
                    <Button
                      size="lg"
                      onClick={() => handlePlayContent(featuredContent)}
                      leftIcon={<Play className="w-5 h-5" />}
                    >
                      Play Now
                    </Button>
                    
                    <Button
                      variant="outline"
                      size="lg"
                      onClick={() => handleContentDetails(featuredContent)}
                      leftIcon={<Info className="w-5 h-5" />}
                    >
                      More Info
                    </Button>
                    
                    <Button
                      variant="ghost"
                      size="lg"
                      leftIcon={<Plus className="w-5 h-5" />}
                      onClick={() => addNotification({
                        type: 'info',
                        title: 'Demo Mode',
                        message: 'My List functionality coming soon!',
                      })}
                    >
                      My List
                    </Button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Content Sections */}
        <div className="relative z-20 -mt-32 pb-20">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="mb-8">
              <h2 className="text-white text-xl font-semibold mb-4">Popular Now</h2>
              <div className="flex space-x-4 overflow-x-auto scrollbar-hide pb-4">
                {popularContent.map((content) => (
                  <ContentCard key={content.id} content={content} />
                ))}
              </div>
            </div>
            
            <div className="mb-8">
              <h2 className="text-white text-xl font-semibold mb-4">Trending This Week</h2>
              <div className="flex space-x-4 overflow-x-auto scrollbar-hide pb-4">
                {popularContent.slice().reverse().map((content) => (
                  <ContentCard key={`trending-${content.id}`} content={content} />
                ))}
              </div>
            </div>
            
            <div className="mb-8">
              <h2 className="text-white text-xl font-semibold mb-4">Recommended for You</h2>
              <div className="flex space-x-4 overflow-x-auto scrollbar-hide pb-4">
                {popularContent.slice(1).map((content) => (
                  <ContentCard key={`recommended-${content.id}`} content={content} />
                ))}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
