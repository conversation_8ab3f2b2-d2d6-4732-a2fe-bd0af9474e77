'use client';

import React, { useEffect, useState } from 'react';
import Head from 'next/head';
import { useRouter } from 'next/router';
import { Play, Plus, Info, Star, Clock } from 'lucide-react';
import { useAuthStore } from '@/store/auth.store';
import { useAppStore } from '@/store/app.store';
import { Content } from '@/types';
import Header from '@/components/layout/Header';
import Button from '@/components/ui/Button';
import { formatDuration, getImageUrl } from '@/lib/utils';

// Mock data for demo
const mockContent: Content[] = [
  {
    id: '1',
    title: 'The Matrix',
    description: 'A computer programmer discovers that reality as he knows it is a simulation.',
    synopsis: '<PERSON>, a computer programmer, is led to fight an underground war against powerful computers who have constructed his entire reality with a system called the Matrix.',
    type: 'MOVIE',
    duration: 136,
    releaseDate: '1999-03-31',
    maturityRating: 'R',
    language: 'en',
    originalLanguage: 'en',
    thumbnail: '/images/matrix-thumb.jpg',
    poster: '/images/matrix-poster.jpg',
    backdrop: '/images/matrix-backdrop.jpg',
    trailer: '/videos/matrix-trailer.mp4',
    videoUrl: '/videos/matrix.mp4',
    genres: ['Action', 'Sci-Fi'],
    tags: ['cyberpunk', 'virtual reality', 'philosophy'],
    cast: [],
    crew: [],
    videoQuality: ['720p', '1080p', '4K'],
    subtitles: {},
    audioTracks: [],
    viewCount: 1500000,
    averageRating: 8.7,
    totalRatings: 250000,
    status: 'PUBLISHED',
    publishedAt: '1999-03-31',
    createdAt: '2024-01-01',
    updatedAt: '2024-01-01',
  },
  {
    id: '2',
    title: 'Inception',
    description: 'A thief who steals corporate secrets through dream-sharing technology.',
    synopsis: 'Dom Cobb is a skilled thief, the absolute best in the dangerous art of extraction, stealing valuable secrets from deep within the subconscious during the dream state.',
    type: 'MOVIE',
    duration: 148,
    releaseDate: '2010-07-16',
    maturityRating: 'PG-13',
    language: 'en',
    originalLanguage: 'en',
    thumbnail: '/images/inception-thumb.jpg',
    poster: '/images/inception-poster.jpg',
    backdrop: '/images/inception-backdrop.jpg',
    trailer: '/videos/inception-trailer.mp4',
    videoUrl: '/videos/inception.mp4',
    genres: ['Action', 'Sci-Fi', 'Thriller'],
    tags: ['dreams', 'heist', 'mind-bending'],
    cast: [],
    crew: [],
    videoQuality: ['720p', '1080p', '4K'],
    subtitles: {},
    audioTracks: [],
    viewCount: 2000000,
    averageRating: 8.8,
    totalRatings: 300000,
    status: 'PUBLISHED',
    publishedAt: '2010-07-16',
    createdAt: '2024-01-01',
    updatedAt: '2024-01-01',
  },
];

export default function DashboardPage() {
  const router = useRouter();
  const { isAuthenticated, currentProfile } = useAuthStore();
  const { addNotification } = useAppStore();
  
  const [featuredContent, setFeaturedContent] = useState<Content | null>(null);
  const [popularContent, setPopularContent] = useState<Content[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    if (!isAuthenticated || !currentProfile) {
      router.push('/auth/login');
      return;
    }

    loadDashboardData();
  }, [isAuthenticated, currentProfile, router]);

  const loadDashboardData = async () => {
    try {
      setIsLoading(true);
      
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      setFeaturedContent(mockContent[0]);
      setPopularContent(mockContent);
      
    } catch (error: any) {
      console.error('Error loading dashboard data:', error);
      addNotification({
        type: 'error',
        title: 'Error',
        message: 'Failed to load content. Please try again.',
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handlePlayContent = (content: Content) => {
    addNotification({
      type: 'info',
      title: 'Demo Mode',
      message: `Playing ${content.title} - Video player coming soon!`,
    });
  };

  const handleContentDetails = (content: Content) => {
    addNotification({
      type: 'info',
      title: 'Demo Mode',
      message: `Showing details for ${content.title} - Details page coming soon!`,
    });
  };

  const ContentCard = ({ content }: { content: Content }) => (
    <div className="w-40 h-60 flex-shrink-0 group cursor-pointer transition-transform hover:scale-105">
      <div className="relative w-full h-full rounded-lg overflow-hidden bg-gray-800">
        <div className="w-full h-full bg-gradient-to-br from-red-600 to-red-800 flex items-center justify-center">
          <span className="text-white font-bold text-lg">{content.title.charAt(0)}</span>
        </div>
        
        {/* Overlay */}
        <div className="absolute inset-0 bg-black/60 opacity-0 group-hover:opacity-100 transition-opacity flex items-center justify-center">
          <div className="text-center space-y-2">
            <Button
              size="sm"
              onClick={() => handlePlayContent(content)}
              leftIcon={<Play className="w-4 h-4" />}
            >
              Play
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={() => handleContentDetails(content)}
              leftIcon={<Info className="w-4 h-4" />}
            >
              Info
            </Button>
          </div>
        </div>

        {/* Rating */}
        {content.averageRating && (
          <div className="absolute top-2 right-2 bg-black/80 rounded px-2 py-1 flex items-center space-x-1">
            <Star className="w-3 h-3 text-yellow-400 fill-current" />
            <span className="text-white text-xs">{content.averageRating.toFixed(1)}</span>
          </div>
        )}
      </div>
      
      <div className="mt-2">
        <h3 className="text-white text-sm font-medium truncate">{content.title}</h3>
        <p className="text-gray-400 text-xs">{content.releaseDate ? new Date(content.releaseDate).getFullYear() : ''}</p>
      </div>
    </div>
  );

  if (isLoading) {
    return (
      <div>
        <Head>
          <title>Dashboard - HyperFlix</title>
        </Head>
        <div className="min-h-screen bg-black">
          <Header />
          <div className="pt-16 flex items-center justify-center min-h-screen">
            <div className="text-center">
              <div className="w-16 h-16 border-4 border-gray-300 border-t-red-600 rounded-full animate-spin mx-auto mb-4"></div>
              <p className="text-white">Loading your personalized content...</p>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div>
      <Head>
        <title>Dashboard - HyperFlix</title>
      </Head>

      <div className="min-h-screen bg-black">
        <Header />
        
        {/* Hero Section */}
        {featuredContent && (
          <div className="relative h-screen">
            <div className="absolute inset-0">
              <div className="w-full h-full bg-gradient-to-br from-red-900 via-black to-gray-900"></div>
              <div className="absolute inset-0 bg-gradient-to-r from-black via-black/50 to-transparent" />
              <div className="absolute inset-0 bg-gradient-to-t from-black via-transparent to-transparent" />
            </div>
            
            <div className="relative z-10 flex items-center h-full">
              <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                <div className="max-w-2xl">
                  <h1 className="text-white text-4xl md:text-6xl font-bold mb-4">
                    {featuredContent.title}
                  </h1>
                  
                  <div className="flex items-center space-x-4 mb-4">
                    {featuredContent.averageRating && (
                      <div className="flex items-center space-x-1">
                        <Star className="w-5 h-5 text-yellow-400 fill-current" />
                        <span className="text-white">{featuredContent.averageRating.toFixed(1)}</span>
                      </div>
                    )}
                    
                    <span className="text-gray-300">
                      {featuredContent.releaseDate ? new Date(featuredContent.releaseDate).getFullYear() : ''}
                    </span>
                    
                    {featuredContent.duration && (
                      <div className="flex items-center space-x-1">
                        <Clock className="w-4 h-4 text-gray-400" />
                        <span className="text-gray-300">{formatDuration(featuredContent.duration)}</span>
                      </div>
                    )}
                    
                    <span className="bg-gray-700 text-white px-2 py-1 rounded text-sm">
                      {featuredContent.maturityRating}
                    </span>
                  </div>
                  
                  <p className="text-gray-300 text-lg mb-8 leading-relaxed">
                    {featuredContent.synopsis || featuredContent.description}
                  </p>
                  
                  <div className="flex space-x-4">
                    <Button
                      size="lg"
                      onClick={() => handlePlayContent(featuredContent)}
                      leftIcon={<Play className="w-5 h-5" />}
                    >
                      Play Now
                    </Button>
                    
                    <Button
                      variant="outline"
                      size="lg"
                      onClick={() => handleContentDetails(featuredContent)}
                      leftIcon={<Info className="w-5 h-5" />}
                    >
                      More Info
                    </Button>
                    
                    <Button
                      variant="ghost"
                      size="lg"
                      leftIcon={<Plus className="w-5 h-5" />}
                    >
                      My List
                    </Button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Content Sections */}
        <div className="relative z-20 -mt-32 pb-20">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="mb-8">
              <h2 className="text-white text-xl font-semibold mb-4">Popular Now</h2>
              <div className="flex space-x-4 overflow-x-auto scrollbar-hide pb-4">
                {popularContent.map((content) => (
                  <ContentCard key={content.id} content={content} />
                ))}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
