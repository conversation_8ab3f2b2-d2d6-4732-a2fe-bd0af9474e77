{"name": "hyperflix-frontend", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"next": "14.2.5", "react": "^18.3.1", "react-dom": "^18.3.1", "typescript": "^5.5.4", "@types/node": "^20.14.12", "@types/react": "^18.3.3", "@types/react-dom": "^18.3.0", "tailwindcss": "^3.4.7", "autoprefixer": "^10.4.19", "postcss": "^8.4.40", "zustand": "^4.5.4", "axios": "^1.7.2", "@tanstack/react-query": "^5.51.1", "lucide-react": "^0.408.0", "react-hook-form": "^7.52.1", "@hookform/resolvers": "^3.9.0", "zod": "^3.23.8", "react-hot-toast": "^2.4.1", "clsx": "^2.1.1", "tailwind-merge": "^2.4.0"}, "devDependencies": {"eslint": "^8.57.0", "eslint-config-next": "14.2.5"}}