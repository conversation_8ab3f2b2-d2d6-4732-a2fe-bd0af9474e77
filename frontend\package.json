{"name": "frontend", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@headlessui/react": "^2.2.4", "@hookform/resolvers": "^5.1.0", "@next-auth/prisma-adapter": "^1.0.7", "@tanstack/react-query": "^5.80.6", "@videojs/http-streaming": "^3.17.0", "axios": "^1.9.0", "clsx": "^2.1.1", "framer-motion": "^12.16.0", "hls.js": "^1.6.5", "lucide-react": "^0.513.0", "next": "15.3.3", "next-auth": "^4.24.11", "react": "^19.0.0", "react-dom": "^19.0.0", "react-hook-form": "^7.57.0", "react-hot-toast": "^2.5.2", "tailwind-merge": "^3.3.0", "video.js": "^8.23.3", "zod": "^3.25.56", "zustand": "^5.0.5"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.3.3", "tailwindcss": "^4", "typescript": "^5"}}