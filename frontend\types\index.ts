// Content Types
export type ContentType = 'MOVIE' | 'SERIES' | 'DOCUMENTARY' | 'SHORT';
export type ContentStatus = 'DRAFT' | 'PUBLISHED' | 'ARCHIVED';

export interface Content {
  id: string;
  title: string;
  description?: string;
  synopsis?: string;
  type: ContentType;
  duration?: number;
  releaseDate?: string;
  maturityRating?: string;
  language?: string;
  originalLanguage?: string;
  thumbnail?: string;
  poster?: string;
  backdrop?: string;
  trailer?: string;
  videoUrl?: string;
  genres: string[];
  tags: string[];
  cast?: any[];
  crew?: any[];
  videoQuality: string[];
  subtitles?: any;
  audioTracks?: any[];
  viewCount: number;
  averageRating?: number;
  totalRatings: number;
  status: ContentStatus;
  publishedAt?: string;
  createdAt: string;
  updatedAt: string;
  seasons?: Season[];
}

export interface Season {
  id: string;
  number: number;
  title: string;
  description?: string;
  poster?: string;
  releaseDate?: string;
  contentId: string;
  episodes: Episode[];
  createdAt: string;
  updatedAt: string;
}

export interface Episode {
  id: string;
  number: number;
  title: string;
  description?: string;
  duration?: number;
  thumbnail?: string;
  videoUrl?: string;
  releaseDate?: string;
  seasonId: string;
  createdAt: string;
  updatedAt: string;
}
