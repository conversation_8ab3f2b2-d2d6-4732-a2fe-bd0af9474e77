// User and Authentication Types
export interface User {
  id: string;
  email: string;
  name: string;
  avatar?: string;
  subscription: 'FREE' | 'BASIC' | 'PREMIUM' | 'FAMILY';
  role: 'USER' | 'ADMIN' | 'CONTENT_MANAGER';
  createdAt: string;
  updatedAt: string;
}

export interface Profile {
  id: string;
  name: string;
  avatar?: string;
  isKid: boolean;
  language: string;
  maturityLevel: string;
  userId: string;
  createdAt: string;
  updatedAt: string;
}

export interface AuthState {
  user: User | null;
  profiles: Profile[];
  currentProfile: Profile | null;
  isLoading: boolean;
  isAuthenticated: boolean;
}

// Content Types
export type ContentType = 'MOVIE' | 'SERIES' | 'DOCUMENTARY' | 'SHORT';
export type ContentStatus = 'DRAFT' | 'PUBLISHED' | 'ARCHIVED';

export interface Content {
  id: string;
  title: string;
  description?: string;
  synopsis?: string;
  type: ContentType;
  duration?: number;
  releaseDate?: string;
  maturityRating?: string;
  language?: string;
  originalLanguage?: string;
  thumbnail?: string;
  poster?: string;
  backdrop?: string;
  trailer?: string;
  videoUrl?: string;
  genres: string[];
  tags: string[];
  cast?: any[];
  crew?: any[];
  videoQuality: string[];
  subtitles?: any;
  audioTracks?: any[];
  viewCount: number;
  averageRating?: number;
  totalRatings: number;
  status: ContentStatus;
  publishedAt?: string;
  createdAt: string;
  updatedAt: string;
  seasons?: Season[];
}

export interface Season {
  id: string;
  number: number;
  title: string;
  description?: string;
  poster?: string;
  releaseDate?: string;
  contentId: string;
  episodes: Episode[];
  createdAt: string;
  updatedAt: string;
}

export interface Episode {
  id: string;
  number: number;
  title: string;
  description?: string;
  duration?: number;
  thumbnail?: string;
  videoUrl?: string;
  releaseDate?: string;
  seasonId: string;
  createdAt: string;
  updatedAt: string;
}

// UI State Types
export interface AppState {
  theme: 'light' | 'dark';
  language: string;
  isLoading: boolean;
  error: string | null;
  notifications: Notification[];
}

export interface Notification {
  id: string;
  type: 'success' | 'error' | 'warning' | 'info';
  title: string;
  message: string;
  duration?: number;
  createdAt: Date;
}

// Form Types
export interface LoginForm {
  email: string;
  password: string;
  rememberMe?: boolean;
}

export interface RegisterForm {
  name: string;
  email: string;
  password: string;
  confirmPassword: string;
  acceptTerms: boolean;
}

// Component Props Types
export interface BaseComponentProps {
  className?: string;
  children?: React.ReactNode;
}

export interface ContentCardProps extends BaseComponentProps {
  content: Content;
  size?: 'small' | 'medium' | 'large';
  showProgress?: boolean;
  onClick?: (content: Content) => void;
}

// API Response Types
export interface ApiResponse<T> {
  data: T;
  message?: string;
  success: boolean;
}

export interface PaginatedResponse<T> {
  data: T[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    pages: number;
  };
}
