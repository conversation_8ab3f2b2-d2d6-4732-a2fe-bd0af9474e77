import { create } from 'zustand';
import { persist } from 'zustand/middleware';
import { User, Profile, AuthState } from '@/types';

interface AuthStore extends AuthState {
  // Actions
  setUser: (user: User | null) => void;
  setProfiles: (profiles: Profile[]) => void;
  setCurrentProfile: (profile: Profile | null) => void;
  setLoading: (loading: boolean) => void;
  login: (user: User, profiles: Profile[]) => void;
  logout: () => void;
  switchProfile: (profile: Profile) => void;
  updateProfile: (profile: Profile) => void;
  addProfile: (profile: Profile) => void;
  removeProfile: (profileId: string) => void;
}

export const useAuthStore = create<AuthStore>()(
  persist(
    (set, get) => ({
      // Initial state
      user: null,
      profiles: [],
      currentProfile: null,
      isLoading: false,
      isAuthenticated: false,

      // Actions
      setUser: (user) => set({ user, isAuthenticated: !!user }),
      
      setProfiles: (profiles) => set({ profiles }),
      
      setCurrentProfile: (profile) => set({ currentProfile: profile }),
      
      setLoading: (loading) => set({ isLoading: loading }),
      
      login: (user, profiles) => set({
        user,
        profiles,
        isAuthenticated: true,
        isLoading: false,
        currentProfile: profiles.length > 0 ? profiles[0] : null,
      }),
      
      logout: () => set({
        user: null,
        profiles: [],
        currentProfile: null,
        isAuthenticated: false,
        isLoading: false,
      }),
      
      switchProfile: (profile) => set({ currentProfile: profile }),
      
      updateProfile: (updatedProfile) => {
        const { profiles, currentProfile } = get();
        const newProfiles = profiles.map(p => 
          p.id === updatedProfile.id ? updatedProfile : p
        );
        set({
          profiles: newProfiles,
          currentProfile: currentProfile?.id === updatedProfile.id ? updatedProfile : currentProfile,
        });
      },
      
      addProfile: (profile) => {
        const { profiles } = get();
        set({ profiles: [...profiles, profile] });
      },
      
      removeProfile: (profileId) => {
        const { profiles, currentProfile } = get();
        const newProfiles = profiles.filter(p => p.id !== profileId);
        set({
          profiles: newProfiles,
          currentProfile: currentProfile?.id === profileId ? 
            (newProfiles.length > 0 ? newProfiles[0] : null) : currentProfile,
        });
      },
    }),
    {
      name: 'auth-storage',
      partialize: (state) => ({
        user: state.user,
        profiles: state.profiles,
        currentProfile: state.currentProfile,
        isAuthenticated: state.isAuthenticated,
      }),
    }
  )
);
