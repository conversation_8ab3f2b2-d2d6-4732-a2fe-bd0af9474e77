import axios, { AxiosInstance, AxiosRequestConfig, AxiosResponse } from 'axios';
import { useAuthStore } from '@/store/auth.store';
import { useAppStore } from '@/store/app.store';

// API Configuration
const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3001/api/v1';

// Create axios instance
const api: AxiosInstance = axios.create({
  baseURL: API_BASE_URL,
  timeout: 30000,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Request interceptor
api.interceptors.request.use(
  (config) => {
    // Add auth token if available
    const token = localStorage.getItem('auth-token');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }

    // Add current profile ID if available
    const authState = useAuthStore.getState();
    if (authState.currentProfile) {
      config.headers['X-Profile-ID'] = authState.currentProfile.id;
    }

    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Response interceptor
api.interceptors.response.use(
  (response: AxiosResponse) => {
    return response;
  },
  (error) => {
    const { response } = error;
    const appStore = useAppStore.getState();
    const authStore = useAuthStore.getState();

    if (response?.status === 401) {
      // Unauthorized - clear auth and redirect to login
      authStore.logout();
      localStorage.removeItem('auth-token');
      window.location.href = '/auth/login';
    } else if (response?.status === 403) {
      // Forbidden
      appStore.addNotification({
        type: 'error',
        title: 'Access Denied',
        message: 'You do not have permission to perform this action.',
      });
    } else if (response?.status >= 500) {
      // Server error
      appStore.addNotification({
        type: 'error',
        title: 'Server Error',
        message: 'Something went wrong on our end. Please try again later.',
      });
    }

    return Promise.reject(error);
  }
);

// API Methods
export const apiClient = {
  // Generic methods
  get: <T>(url: string, config?: AxiosRequestConfig) => 
    api.get<T>(url, config).then(res => res.data),
  
  post: <T>(url: string, data?: any, config?: AxiosRequestConfig) => 
    api.post<T>(url, data, config).then(res => res.data),
  
  put: <T>(url: string, data?: any, config?: AxiosRequestConfig) => 
    api.put<T>(url, data, config).then(res => res.data),
  
  patch: <T>(url: string, data?: any, config?: AxiosRequestConfig) => 
    api.patch<T>(url, data, config).then(res => res.data),
  
  delete: <T>(url: string, config?: AxiosRequestConfig) => 
    api.delete<T>(url, config).then(res => res.data),

  // Auth methods
  auth: {
    login: (email: string, password: string) =>
      apiClient.post('/auth/login', { email, password }),
    
    register: (data: { name: string; email: string; password: string }) =>
      apiClient.post('/auth/register', data),
    
    logout: () =>
      apiClient.post('/auth/logout'),
    
    refresh: () =>
      apiClient.post('/auth/refresh'),
    
    me: () =>
      apiClient.get('/auth/me'),
  },

  // Users methods
  users: {
    getMe: () =>
      apiClient.get('/users/me'),
    
    updateMe: (data: any) =>
      apiClient.put('/users/me', data),
    
    getStats: () =>
      apiClient.get('/users/me/stats'),
  },

  // Content methods
  content: {
    getAll: (params?: any) =>
      apiClient.get('/content', { params }),
    
    getById: (id: string) =>
      apiClient.get(`/content/${id}`),
    
    getPopular: (params?: any) =>
      apiClient.get('/content/popular', { params }),
    
    getRecent: (params?: any) =>
      apiClient.get('/content/recent', { params }),
    
    getTopRated: (params?: any) =>
      apiClient.get('/content/top-rated', { params }),
    
    getGenres: () =>
      apiClient.get('/content/genres'),
    
    getLanguages: () =>
      apiClient.get('/content/languages'),
    
    getStats: (id: string) =>
      apiClient.get(`/content/${id}/stats`),
  },

  // Streaming methods
  streaming: {
    getStreamUrl: (contentId: string) =>
      apiClient.get(`/streaming/content/${contentId}/stream`),
    
    getEpisodeStreamUrl: (episodeId: string) =>
      apiClient.get(`/streaming/episode/${episodeId}/stream`),
    
    getQualities: (contentId: string) =>
      apiClient.get(`/streaming/content/${contentId}/qualities`),
    
    updateProgress: (data: any) =>
      apiClient.put('/streaming/progress', data),
    
    getProgress: (contentId: string) =>
      apiClient.get(`/streaming/progress/${contentId}`),
    
    requestDownload: (contentId: string, quality: string) =>
      apiClient.post(`/streaming/download/${contentId}`, { quality }),
    
    getDownloadUrl: (token: string) =>
      apiClient.get(`/streaming/download/${token}`),
    
    getManifest: (contentId: string, format: 'hls' | 'dash') =>
      apiClient.get(`/streaming/manifest/${contentId}/${format}`),
    
    getThumbnails: (contentId: string) =>
      apiClient.get(`/streaming/thumbnails/${contentId}`),
  },

  // AI methods
  ai: {
    getRecommendations: (profileId: string, params?: any) =>
      apiClient.get(`/ai/recommendations/${profileId}`, { params }),
    
    getSimilar: (contentId: string, params?: any) =>
      apiClient.get(`/ai/recommendations/similar/${contentId}`, { params }),
    
    getTrending: (params?: any) =>
      apiClient.get('/ai/recommendations/trending', { params }),
    
    getContinueWatching: (profileId: string) =>
      apiClient.get(`/ai/recommendations/continue-watching/${profileId}`),
    
    search: (query: string, filters?: any) =>
      apiClient.post('/ai/search', { query, filters }),
    
    semanticSearch: (query: string, filters?: any) =>
      apiClient.post('/ai/search/semantic', { query, filters }),
    
    getSearchSuggestions: (query: string) =>
      apiClient.get('/ai/search/suggestions', { params: { q: query } }),
    
    voiceSearch: (audioBlob: Blob) => {
      const formData = new FormData();
      formData.append('audio', audioBlob);
      return apiClient.post('/ai/search/voice', formData, {
        headers: { 'Content-Type': 'multipart/form-data' },
      });
    },
    
    getSubtitles: (contentId: string, language?: string) =>
      apiClient.get(`/ai/subtitles/${contentId}`, { params: { language } }),
    
    getSubtitleLanguages: (contentId: string) =>
      apiClient.get(`/ai/subtitles/${contentId}/languages`),
  },

  // Analytics methods
  analytics: {
    trackInteraction: (data: any) =>
      apiClient.post('/streaming/analytics/interaction', data),
    
    getContentAnalytics: (contentId: string) =>
      apiClient.get(`/streaming/analytics/content/${contentId}`),
    
    getUserAnalytics: () =>
      apiClient.get('/streaming/analytics/user'),
    
    getPlatformAnalytics: () =>
      apiClient.get('/streaming/analytics/platform'),
  },
};

export default api;
