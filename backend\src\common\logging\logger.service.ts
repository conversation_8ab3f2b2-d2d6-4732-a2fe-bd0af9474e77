import { Injectable, LoggerService, Scope } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import * as winston from 'winston';
import * as DailyRotateFile from 'winston-daily-rotate-file';

@Injectable({ scope: Scope.TRANSIENT })
export class CustomLoggerService implements LoggerService {
  private logger: winston.Logger;
  private context?: string;

  constructor(private readonly configService: ConfigService) {
    this.createLogger();
  }

  setContext(context: string) {
    this.context = context;
  }

  log(message: any, context?: string) {
    this.logger.info(message, { context: context || this.context });
  }

  error(message: any, trace?: string, context?: string) {
    this.logger.error(message, {
      context: context || this.context,
      trace,
    });
  }

  warn(message: any, context?: string) {
    this.logger.warn(message, { context: context || this.context });
  }

  debug(message: any, context?: string) {
    this.logger.debug(message, { context: context || this.context });
  }

  verbose(message: any, context?: string) {
    this.logger.verbose(message, { context: context || this.context });
  }

  private createLogger() {
    const logLevel = this.configService.get('LOG_LEVEL', 'info');
    const nodeEnv = this.configService.get('NODE_ENV', 'development');

    const transports: winston.transport[] = [];

    // Console transport for development
    if (nodeEnv === 'development') {
      transports.push(
        new winston.transports.Console({
          format: winston.format.combine(
            winston.format.colorize(),
            winston.format.timestamp(),
            winston.format.printf(({ timestamp, level, message, context, trace }) => {
              const contextStr = context ? `[${context}] ` : '';
              const traceStr = trace ? `\n${trace}` : '';
              return `${timestamp} ${level}: ${contextStr}${message}${traceStr}`;
            }),
          ),
        }),
      );
    }

    // File transports for production
    if (nodeEnv === 'production') {
      // Error logs
      transports.push(
        new DailyRotateFile({
          filename: 'logs/error-%DATE%.log',
          datePattern: 'YYYY-MM-DD',
          level: 'error',
          maxSize: '20m',
          maxFiles: '14d',
          format: winston.format.combine(
            winston.format.timestamp(),
            winston.format.json(),
          ),
        }),
      );

      // Combined logs
      transports.push(
        new DailyRotateFile({
          filename: 'logs/combined-%DATE%.log',
          datePattern: 'YYYY-MM-DD',
          maxSize: '20m',
          maxFiles: '14d',
          format: winston.format.combine(
            winston.format.timestamp(),
            winston.format.json(),
          ),
        }),
      );
    }

    this.logger = winston.createLogger({
      level: logLevel,
      format: winston.format.combine(
        winston.format.timestamp(),
        winston.format.errors({ stack: true }),
        winston.format.json(),
      ),
      defaultMeta: {
        service: 'hyperflix-backend',
        environment: nodeEnv,
      },
      transports,
    });

    // Handle uncaught exceptions and unhandled rejections
    this.logger.exceptions.handle(
      new winston.transports.File({ filename: 'logs/exceptions.log' }),
    );

    this.logger.rejections.handle(
      new winston.transports.File({ filename: 'logs/rejections.log' }),
    );
  }

  // Custom methods for specific log types
  logApiRequest(req: any, res: any, responseTime: number) {
    this.logger.info('API Request', {
      method: req.method,
      url: req.url,
      statusCode: res.statusCode,
      responseTime: `${responseTime}ms`,
      userAgent: req.get('User-Agent'),
      ip: req.ip,
      userId: req.user?.id,
    });
  }

  logVideoProcessing(contentId: string, quality: string, status: string, duration?: number) {
    this.logger.info('Video Processing', {
      contentId,
      quality,
      status,
      duration: duration ? `${duration}ms` : undefined,
    });
  }

  logStreamingEvent(event: string, data: any) {
    this.logger.info('Streaming Event', {
      event,
      ...data,
    });
  }

  logSecurityEvent(event: string, data: any) {
    this.logger.warn('Security Event', {
      event,
      ...data,
    });
  }

  logPerformanceMetric(metric: string, value: number, unit: string) {
    this.logger.info('Performance Metric', {
      metric,
      value,
      unit,
      timestamp: new Date().toISOString(),
    });
  }
}
