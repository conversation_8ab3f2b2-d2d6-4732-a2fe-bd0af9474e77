import { Injectable, Logger } from '@nestjs/common';
import { PrismaService } from '../../common/prisma/prisma.service';
import { AiService } from '../ai.service';

interface SearchResult {
  content: any;
  relevanceScore: number;
  matchedFields: string[];
  aiSummary?: string;
}

@Injectable()
export class SearchService {
  private readonly logger = new Logger(SearchService.name);

  constructor(
    private readonly prisma: PrismaService,
    private readonly aiService: AiService,
  ) {}

  async intelligentSearch(
    query: string,
    profileId?: string,
    filters?: {
      genres?: string[];
      type?: string;
      maturityRating?: string;
      language?: string;
      releaseYear?: number;
    },
    limit: number = 20,
  ): Promise<SearchResult[]> {
    try {
      // First, enhance the search query with AI
      const enhancedQuery = await this.enhanceSearchQuery(query);

      // Perform basic text search
      const basicResults = await this.performBasicSearch(enhancedQuery, filters, limit * 2);

      // Apply AI-powered ranking and filtering
      const rankedResults = await this.rankSearchResults(basicResults, query, profileId);

      // Generate AI summaries for top results
      const resultsWithSummaries = await this.addAISummaries(
        rankedResults.slice(0, limit),
        query,
      );

      return resultsWithSummaries;
    } catch (error) {
      this.logger.error('Error performing intelligent search:', error);
      throw error;
    }
  }

  async semanticSearch(
    query: string,
    profileId?: string,
    limit: number = 10,
  ): Promise<SearchResult[]> {
    try {
      // Use AI to understand the semantic meaning of the query
      const semanticAnalysis = await this.analyzeSearchIntent(query);

      // Search based on semantic understanding
      const results = await this.searchBySemantics(semanticAnalysis, profileId, limit);

      return results;
    } catch (error) {
      this.logger.error('Error performing semantic search:', error);
      throw error;
    }
  }

  async searchSuggestions(partialQuery: string, limit: number = 5): Promise<string[]> {
    try {
      // Get popular search terms that match the partial query
      const popularTerms = await this.getPopularSearchTerms(partialQuery, limit);

      // Use AI to generate intelligent suggestions
      const aiSuggestions = await this.generateAISuggestions(partialQuery);

      // Combine and deduplicate
      const allSuggestions = [...popularTerms, ...aiSuggestions];
      const uniqueSuggestions = [...new Set(allSuggestions)];

      return uniqueSuggestions.slice(0, limit);
    } catch (error) {
      this.logger.error('Error generating search suggestions:', error);
      return [];
    }
  }

  async searchByVoice(audioBuffer: Buffer, profileId?: string): Promise<SearchResult[]> {
    try {
      // Transcribe voice to text
      const transcription = await this.aiService.transcribeAudio(audioBuffer);

      // Perform intelligent search with the transcribed text
      return this.intelligentSearch(transcription, profileId);
    } catch (error) {
      this.logger.error('Error performing voice search:', error);
      throw error;
    }
  }

  async searchByImage(imageDescription: string, profileId?: string): Promise<SearchResult[]> {
    try {
      // Use AI to understand what the user is looking for based on image description
      const searchQuery = await this.generateQueryFromImageDescription(imageDescription);

      return this.intelligentSearch(searchQuery, profileId);
    } catch (error) {
      this.logger.error('Error performing image search:', error);
      throw error;
    }
  }

  private async enhanceSearchQuery(query: string): Promise<string> {
    const prompt = `
      Enhance the following search query to improve content discovery:
      
      Original query: "${query}"
      
      Provide an enhanced version that includes:
      - Synonyms and related terms
      - Corrected spelling if needed
      - Expanded context
      
      Return only the enhanced query text.
    `;

    return this.aiService.generateText(prompt, { maxTokens: 100, temperature: 0.3 });
  }

  private async performBasicSearch(
    query: string,
    filters?: any,
    limit: number = 40,
  ): Promise<any[]> {
    const searchTerms = query.toLowerCase().split(' ').filter(term => term.length > 2);

    const whereConditions: any = {
      AND: [
        { status: 'PUBLISHED' },
        {
          OR: [
            {
              title: {
                contains: query,
                mode: 'insensitive',
              },
            },
            {
              description: {
                contains: query,
                mode: 'insensitive',
              },
            },
            {
              synopsis: {
                contains: query,
                mode: 'insensitive',
              },
            },
            {
              tags: {
                hasSome: searchTerms,
              },
            },
            {
              genres: {
                hasSome: searchTerms,
              },
            },
          ],
        },
      ],
    };

    // Apply filters
    if (filters) {
      if (filters.genres?.length) {
        whereConditions.AND.push({
          genres: { hasSome: filters.genres },
        });
      }
      if (filters.type) {
        whereConditions.AND.push({ type: filters.type });
      }
      if (filters.maturityRating) {
        whereConditions.AND.push({ maturityRating: filters.maturityRating });
      }
      if (filters.language) {
        whereConditions.AND.push({ language: filters.language });
      }
      if (filters.releaseYear) {
        whereConditions.AND.push({
          releaseDate: {
            gte: new Date(`${filters.releaseYear}-01-01`),
            lt: new Date(`${filters.releaseYear + 1}-01-01`),
          },
        });
      }
    }

    return this.prisma.content.findMany({
      where: whereConditions,
      take: limit,
      orderBy: [
        { averageRating: 'desc' },
        { viewCount: 'desc' },
      ],
    });
  }

  private async rankSearchResults(
    results: any[],
    originalQuery: string,
    profileId?: string,
  ): Promise<SearchResult[]> {
    const rankedResults: SearchResult[] = [];

    for (const content of results) {
      const relevanceScore = this.calculateRelevanceScore(content, originalQuery);
      const matchedFields = this.getMatchedFields(content, originalQuery);

      rankedResults.push({
        content,
        relevanceScore,
        matchedFields,
      });
    }

    // Sort by relevance score
    rankedResults.sort((a, b) => b.relevanceScore - a.relevanceScore);

    // Apply personalization if profile is provided
    if (profileId) {
      return this.personalizeSearchResults(rankedResults, profileId);
    }

    return rankedResults;
  }

  private calculateRelevanceScore(content: any, query: string): number {
    let score = 0;
    const queryLower = query.toLowerCase();

    // Title match (highest weight)
    if (content.title.toLowerCase().includes(queryLower)) {
      score += 100;
    }

    // Description match
    if (content.description?.toLowerCase().includes(queryLower)) {
      score += 50;
    }

    // Genre match
    const queryTerms = queryLower.split(' ');
    const genreMatches = content.genres.filter((genre: string) =>
      queryTerms.some(term => genre.toLowerCase().includes(term)),
    ).length;
    score += genreMatches * 30;

    // Tag match
    const tagMatches = content.tags.filter((tag: string) =>
      queryTerms.some(term => tag.toLowerCase().includes(term)),
    ).length;
    score += tagMatches * 20;

    // Quality bonus
    score += (content.averageRating || 0) * 10;
    score += Math.log(content.viewCount + 1) * 5;

    return score;
  }

  private getMatchedFields(content: any, query: string): string[] {
    const matchedFields: string[] = [];
    const queryLower = query.toLowerCase();

    if (content.title.toLowerCase().includes(queryLower)) {
      matchedFields.push('title');
    }
    if (content.description?.toLowerCase().includes(queryLower)) {
      matchedFields.push('description');
    }
    if (content.genres.some((genre: string) => genre.toLowerCase().includes(queryLower))) {
      matchedFields.push('genres');
    }
    if (content.tags.some((tag: string) => tag.toLowerCase().includes(queryLower))) {
      matchedFields.push('tags');
    }

    return matchedFields;
  }

  private async addAISummaries(
    results: SearchResult[],
    query: string,
  ): Promise<SearchResult[]> {
    return Promise.all(
      results.map(async (result) => {
        try {
          const prompt = `
            Based on the search query "${query}", provide a brief explanation of why this content is relevant:
            
            Title: ${result.content.title}
            Description: ${result.content.description}
            Genres: ${result.content.genres.join(', ')}
            
            Provide a 1-2 sentence explanation focusing on the connection to the search query.
          `;

          const aiSummary = await this.aiService.generateText(prompt, {
            maxTokens: 100,
            temperature: 0.5,
          });

          return {
            ...result,
            aiSummary: aiSummary.trim(),
          };
        } catch (error) {
          this.logger.warn('Failed to generate AI summary for search result');
          return result;
        }
      }),
    );
  }

  private async analyzeSearchIntent(query: string): Promise<any> {
    const prompt = `
      Analyze the following search query and extract the user's intent:
      
      Query: "${query}"
      
      Provide a JSON response with:
      - intent: (discover, specific_title, genre_browse, mood_based, actor_search, etc.)
      - keywords: array of important keywords
      - genres: array of relevant genres
      - mood: emotional tone or atmosphere
      - filters: suggested filters (year, rating, etc.)
      
      Example: {"intent": "mood_based", "keywords": ["action", "thriller"], "genres": ["Action", "Thriller"], "mood": "exciting", "filters": {}}
    `;

    const response = await this.aiService.generateText(prompt, { maxTokens: 300 });
    
    try {
      return JSON.parse(response);
    } catch (error) {
      this.logger.warn('Failed to parse semantic analysis response');
      return {
        intent: 'discover',
        keywords: query.split(' '),
        genres: [],
        mood: 'neutral',
        filters: {},
      };
    }
  }

  private async searchBySemantics(
    semanticAnalysis: any,
    profileId?: string,
    limit: number = 10,
  ): Promise<SearchResult[]> {
    // This would implement more sophisticated semantic search
    // For now, we'll use the enhanced basic search
    const enhancedQuery = semanticAnalysis.keywords.join(' ');
    return this.intelligentSearch(enhancedQuery, profileId, {
      genres: semanticAnalysis.genres,
    }, limit);
  }

  private async getPopularSearchTerms(partialQuery: string, limit: number): Promise<string[]> {
    // This would typically query a search analytics table
    // For now, return some mock popular terms
    const mockPopularTerms = [
      'action movies',
      'comedy series',
      'thriller films',
      'documentary',
      'sci-fi',
      'romance',
      'horror',
      'drama',
    ];

    return mockPopularTerms
      .filter(term => term.toLowerCase().includes(partialQuery.toLowerCase()))
      .slice(0, limit);
  }

  private async generateAISuggestions(partialQuery: string): Promise<string[]> {
    const prompt = `
      Generate 3 search suggestions based on this partial query: "${partialQuery}"
      
      Provide suggestions that complete or enhance the query for a streaming platform.
      Return only the suggestions, one per line.
    `;

    const response = await this.aiService.generateText(prompt, {
      maxTokens: 100,
      temperature: 0.7,
    });

    return response.split('\n').filter(line => line.trim().length > 0).slice(0, 3);
  }

  private async generateQueryFromImageDescription(description: string): Promise<string> {
    const prompt = `
      Based on this image description, generate a search query for a streaming platform:
      
      Description: "${description}"
      
      Generate a search query that would help find relevant movies or TV shows.
      Return only the search query.
    `;

    return this.aiService.generateText(prompt, { maxTokens: 50, temperature: 0.5 });
  }

  private async personalizeSearchResults(
    results: SearchResult[],
    profileId: string,
  ): Promise<SearchResult[]> {
    try {
      // Get user preferences
      const profile = await this.prisma.profile.findUnique({
        where: { id: profileId },
        include: {
          favorites: { include: { content: true } },
          ratings: { include: { content: true } },
          watchHistory: { include: { content: true } },
        },
      });

      if (!profile) return results;

      // Extract user preferences
      const userGenres = [
        ...profile.favorites.flatMap(f => f.content.genres),
        ...profile.ratings.filter(r => r.rating >= 4).flatMap(r => r.content.genres),
      ];

      const genrePreferences = this.getGenreFrequency(userGenres);

      // Boost scores based on user preferences
      return results.map(result => ({
        ...result,
        relevanceScore: result.relevanceScore + this.calculatePersonalizationBoost(
          result.content,
          genrePreferences,
        ),
      })).sort((a, b) => b.relevanceScore - a.relevanceScore);
    } catch (error) {
      this.logger.warn('Failed to personalize search results');
      return results;
    }
  }

  private getGenreFrequency(genres: string[]): Record<string, number> {
    const frequency: Record<string, number> = {};
    genres.forEach(genre => {
      frequency[genre] = (frequency[genre] || 0) + 1;
    });
    return frequency;
  }

  private calculatePersonalizationBoost(
    content: any,
    genrePreferences: Record<string, number>,
  ): number {
    let boost = 0;
    content.genres.forEach((genre: string) => {
      boost += (genrePreferences[genre] || 0) * 5;
    });
    return boost;
  }
}
