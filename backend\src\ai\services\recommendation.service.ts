import { Injectable, Logger } from '@nestjs/common';
import { PrismaService } from '../../common/prisma/prisma.service';
import { AiService } from '../ai.service';
import { RecommendationType } from '@prisma/client';

@Injectable()
export class RecommendationService {
  private readonly logger = new Logger(RecommendationService.name);

  constructor(
    private readonly prisma: PrismaService,
    private readonly aiService: AiService,
  ) {}

  async generatePersonalizedRecommendations(
    profileId: string,
    limit: number = 10,
  ): Promise<any[]> {
    try {
      // Get user's watch history and preferences
      const profile = await this.prisma.profile.findUnique({
        where: { id: profileId },
        include: {
          watchHistory: {
            include: { content: true },
            orderBy: { watchedAt: 'desc' },
            take: 100, // Increased for better analysis
          },
          favorites: {
            include: { content: true },
          },
          ratings: {
            include: { content: true },
            where: { rating: { gte: 4 } },
          },
        },
      });

      if (!profile) {
        throw new Error('Profile not found');
      }

      // Advanced user behavior analysis
      const userBehaviorData = await this.analyzeUserBehaviorAdvanced(profile);

      // Get collaborative filtering recommendations
      const collaborativeRecs = await this.getCollaborativeFilteringRecommendations(
        profileId,
        userBehaviorData,
        limit,
      );

      // Get content-based filtering recommendations
      const contentBasedRecs = await this.getContentBasedRecommendations(
        profile,
        userBehaviorData,
        limit,
      );

      // Hybrid approach: combine collaborative and content-based
      const hybridRecommendations = await this.combineRecommendations(
        collaborativeRecs,
        contentBasedRecs,
        userBehaviorData,
        limit,
      );

      // Apply diversity and freshness filters
      const diversifiedRecs = await this.applyDiversityFilter(
        hybridRecommendations,
        userBehaviorData,
      );

      // Generate AI-powered explanations
      const finalRecommendations = await Promise.all(
        diversifiedRecs.slice(0, limit).map(async (rec) => {
          const explanation = await this.generateAIExplanation(rec, userBehaviorData);
          return {
            ...rec,
            explanation,
            confidence: this.calculateConfidenceScore(rec, userBehaviorData),
          };
        }),
      );

      // Save recommendations to database for analytics
      await this.saveRecommendations(profileId, finalRecommendations);

      return finalRecommendations;
    } catch (error) {
      this.logger.error('Error generating personalized recommendations:', error);
      throw error;
    }
  }

  private async analyzeUserBehaviorAdvanced(profile: any): Promise<{
    genrePreferences: Map<string, number>;
    timePreferences: { hour: number; dayOfWeek: number }[];
    completionRate: number;
    averageRating: number;
    preferredDuration: { min: number; max: number };
    recentTrends: string[];
    seasonality: Map<string, number>;
  }> {
    const genrePreferences = new Map<string, number>();
    const timePreferences: { hour: number; dayOfWeek: number }[] = [];
    let totalWatchTime = 0;
    let completedContent = 0;
    let totalRatings = 0;
    let ratingSum = 0;
    const durations: number[] = [];
    const recentGenres: string[] = [];

    // Analyze watch history
    profile.watchHistory.forEach((wh: any) => {
      if (wh.content) {
        // Genre preferences with recency weighting
        const recencyWeight = this.calculateRecencyWeight(wh.watchedAt);
        wh.content.genres.forEach((genre: string) => {
          const currentScore = genrePreferences.get(genre) || 0;
          genrePreferences.set(genre, currentScore + recencyWeight);
        });

        // Time preferences
        const watchTime = new Date(wh.watchedAt);
        timePreferences.push({
          hour: watchTime.getHours(),
          dayOfWeek: watchTime.getDay(),
        });

        // Completion analysis
        totalWatchTime += wh.progress || 0;
        if (wh.completed) completedContent++;

        // Duration preferences
        if (wh.content.duration) {
          durations.push(wh.content.duration);
        }

        // Recent trends (last 30 days)
        if (this.isRecent(wh.watchedAt, 30)) {
          recentGenres.push(...wh.content.genres);
        }
      }
    });

    // Analyze ratings
    profile.ratings.forEach((rating: any) => {
      totalRatings++;
      ratingSum += rating.rating;
    });

    // Calculate preferred duration range
    const sortedDurations = durations.sort((a, b) => a - b);
    const preferredDuration = {
      min: sortedDurations[Math.floor(sortedDurations.length * 0.25)] || 60,
      max: sortedDurations[Math.floor(sortedDurations.length * 0.75)] || 120,
    };

    // Identify recent trends
    const recentTrends = this.getTopItems(recentGenres, 5);

    return {
      genrePreferences,
      timePreferences,
      completionRate: profile.watchHistory.length > 0 ? completedContent / profile.watchHistory.length : 0,
      averageRating: totalRatings > 0 ? ratingSum / totalRatings : 0,
      preferredDuration,
      recentTrends,
      seasonality: this.analyzeSeasonality(profile.watchHistory),
    };
  }

  private async getCollaborativeFilteringRecommendations(
    profileId: string,
    userBehavior: any,
    limit: number,
  ): Promise<any[]> {
    // Find similar users based on viewing patterns
    const similarUsers = await this.findSimilarUsers(profileId, userBehavior);

    // Get content liked by similar users but not watched by current user
    const recommendations: any[] = [];

    for (const similarUser of similarUsers.slice(0, 10)) {
      const theirFavorites = await this.prisma.favorite.findMany({
        where: { userId: similarUser.userId },
        include: { content: true },
      });

      theirFavorites.forEach(fav => {
        if (!this.hasUserWatched(profileId, fav.contentId)) {
          recommendations.push({
            content: fav.content,
            score: similarUser.similarity * 0.8, // Collaborative filtering weight
            type: 'collaborative',
            source: 'similar_users',
          });
        }
      });
    }

    return recommendations.slice(0, limit);
  }

  private async getContentBasedRecommendations(
    profile: any,
    userBehavior: any,
    limit: number,
  ): Promise<any[]> {
    const recommendations: any[] = [];

    // Get content similar to highly rated content
    const highRatedContent = profile.ratings
      .filter((r: any) => r.rating >= 4)
      .map((r: any) => r.content);

    for (const content of highRatedContent) {
      const similarContent = await this.findSimilarContent(content, userBehavior);
      recommendations.push(...similarContent);
    }

    // Get content based on favorite genres
    const topGenres = Array.from(userBehavior.genrePreferences.entries())
      .sort(([,a], [,b]) => b - a)
      .slice(0, 3)
      .map(([genre]) => genre);

    const genreBasedContent = await this.getContentByGenres(topGenres, limit);
    recommendations.push(...genreBasedContent.map(content => ({
      content,
      score: this.calculateGenreMatchScore(content, userBehavior.genrePreferences),
      type: 'content_based',
      source: 'genre_preference',
    })));

    return recommendations;
  }

  private async combineRecommendations(
    collaborative: any[],
    contentBased: any[],
    userBehavior: any,
    limit: number,
  ): Promise<any[]> {
    // Combine and weight recommendations
    const combined = [...collaborative, ...contentBased];

    // Remove duplicates and calculate final scores
    const uniqueRecs = new Map();

    combined.forEach(rec => {
      const contentId = rec.content.id;
      if (uniqueRecs.has(contentId)) {
        const existing = uniqueRecs.get(contentId);
        existing.score = Math.max(existing.score, rec.score);
        existing.sources = [...(existing.sources || []), rec.source];
      } else {
        uniqueRecs.set(contentId, {
          ...rec,
          sources: [rec.source],
        });
      }
    });

    // Apply additional scoring factors
    const scoredRecs = Array.from(uniqueRecs.values()).map(rec => ({
      ...rec,
      score: this.calculateFinalScore(rec, userBehavior),
    }));

    return scoredRecs.sort((a, b) => b.score - a.score).slice(0, limit * 2);
  }

  private calculateRecencyWeight(watchedAt: Date): number {
    const daysSince = (Date.now() - watchedAt.getTime()) / (1000 * 60 * 60 * 24);
    return Math.exp(-daysSince / 30); // Exponential decay over 30 days
  }

  private isRecent(date: Date, days: number): boolean {
    const daysSince = (Date.now() - date.getTime()) / (1000 * 60 * 60 * 24);
    return daysSince <= days;
  }

  private getTopItems(items: string[], count: number): string[] {
    const frequency = new Map<string, number>();
    items.forEach(item => {
      frequency.set(item, (frequency.get(item) || 0) + 1);
    });

    return Array.from(frequency.entries())
      .sort(([,a], [,b]) => b - a)
      .slice(0, count)
      .map(([item]) => item);
  }

  private analyzeSeasonality(watchHistory: any[]): Map<string, number> {
    const seasonality = new Map<string, number>();

    watchHistory.forEach(wh => {
      const month = new Date(wh.watchedAt).getMonth();
      const season = this.getSeasonFromMonth(month);

      wh.content?.genres.forEach((genre: string) => {
        const key = `${season}_${genre}`;
        seasonality.set(key, (seasonality.get(key) || 0) + 1);
      });
    });

    return seasonality;
  }

  private getSeasonFromMonth(month: number): string {
    if (month >= 2 && month <= 4) return 'spring';
    if (month >= 5 && month <= 7) return 'summer';
    if (month >= 8 && month <= 10) return 'fall';
    return 'winter';
  }

  private async findSimilarUsers(profileId: string, userBehavior: any): Promise<any[]> {
    // Find users with similar viewing patterns
    const similarUsers = await this.prisma.profile.findMany({
      where: {
        id: { not: profileId },
      },
      include: {
        watchHistory: {
          include: { content: true },
          take: 50,
        },
        favorites: {
          include: { content: true },
        },
      },
      take: 100,
    });

    // Calculate similarity scores
    const similarities = similarUsers.map(user => ({
      userId: user.userId,
      similarity: this.calculateUserSimilarity(userBehavior, user),
    }));

    return similarities
      .filter(s => s.similarity > 0.1)
      .sort((a, b) => b.similarity - a.similarity);
  }

  private calculateUserSimilarity(userBehavior: any, otherUser: any): number {
    // Simple Jaccard similarity based on genres
    const userGenres = new Set(Array.from(userBehavior.genrePreferences.keys()));
    const otherGenres = new Set();

    otherUser.watchHistory.forEach((wh: any) => {
      wh.content?.genres.forEach((genre: string) => otherGenres.add(genre));
    });

    const intersection = new Set([...userGenres].filter(x => otherGenres.has(x)));
    const union = new Set([...userGenres, ...otherGenres]);

    return intersection.size / union.size;
  }

  private hasUserWatched(profileId: string, contentId: string): boolean {
    // This would typically check the database, for now return false
    return false;
  }

  private async findSimilarContent(content: any, userBehavior: any): Promise<any[]> {
    const similarContent = await this.prisma.content.findMany({
      where: {
        AND: [
          { id: { not: content.id } },
          { status: 'PUBLISHED' },
          {
            OR: [
              { genres: { hasSome: content.genres } },
              { tags: { hasSome: content.tags } },
            ],
          },
        ],
      },
      take: 10,
    });

    return similarContent.map(c => ({
      content: c,
      score: this.calculateContentSimilarity(content, c),
      type: 'content_based',
      source: 'similar_content',
    }));
  }

  private calculateContentSimilarity(content1: any, content2: any): number {
    const genres1 = new Set(content1.genres);
    const genres2 = new Set(content2.genres);
    const genreIntersection = new Set([...genres1].filter(x => genres2.has(x)));

    return genreIntersection.size / Math.max(genres1.size, genres2.size);
  }

  private async getContentByGenres(genres: string[], limit: number): Promise<any[]> {
    return this.prisma.content.findMany({
      where: {
        AND: [
          { status: 'PUBLISHED' },
          { genres: { hasSome: genres } },
        ],
      },
      orderBy: { averageRating: 'desc' },
      take: limit,
    });
  }

  private calculateGenreMatchScore(content: any, genrePreferences: Map<string, number>): number {
    let score = 0;
    content.genres.forEach((genre: string) => {
      score += genrePreferences.get(genre) || 0;
    });
    return score;
  }

  private calculateFinalScore(rec: any, userBehavior: any): number {
    let score = rec.score;

    // Boost based on user preferences
    if (rec.content.genres) {
      rec.content.genres.forEach((genre: string) => {
        score += (userBehavior.genrePreferences.get(genre) || 0) * 0.1;
      });
    }

    // Boost recent content
    if (rec.content.publishedAt) {
      const daysSincePublished = (Date.now() - new Date(rec.content.publishedAt).getTime()) / (1000 * 60 * 60 * 24);
      if (daysSincePublished < 30) {
        score *= 1.2; // 20% boost for content published in last 30 days
      }
    }

    // Boost highly rated content
    if (rec.content.averageRating) {
      score *= (1 + rec.content.averageRating / 10);
    }

    return score;
  }

  private async applyDiversityFilter(recommendations: any[], userBehavior: any): Promise<any[]> {
    // Ensure diversity in genres
    const diversified: any[] = [];
    const genreCounts = new Map<string, number>();

    for (const rec of recommendations) {
      let shouldAdd = true;

      // Check genre diversity
      for (const genre of rec.content.genres || []) {
        const count = genreCounts.get(genre) || 0;
        if (count >= 3) { // Max 3 items per genre
          shouldAdd = false;
          break;
        }
      }

      if (shouldAdd) {
        diversified.push(rec);
        rec.content.genres?.forEach((genre: string) => {
          genreCounts.set(genre, (genreCounts.get(genre) || 0) + 1);
        });
      }
    }

    return diversified;
  }

  private async generateAIExplanation(rec: any, userBehavior: any): Promise<string> {
    // Generate explanation based on recommendation source and user behavior
    const topGenres = Array.from(userBehavior.genrePreferences.entries())
      .sort(([,a], [,b]) => b - a)
      .slice(0, 3)
      .map(([genre]) => genre);

    const matchingGenres = rec.content.genres?.filter((genre: string) =>
      topGenres.includes(genre)
    ) || [];

    if (matchingGenres.length > 0) {
      return `Recommended because you enjoy ${matchingGenres.join(' and ')} content`;
    }

    if (rec.type === 'collaborative') {
      return 'Recommended based on users with similar tastes';
    }

    return 'Recommended for you';
  }

  private calculateConfidenceScore(rec: any, userBehavior: any): number {
    let confidence = 0.5; // Base confidence

    // Increase confidence based on genre match
    const matchingGenres = rec.content.genres?.filter((genre: string) =>
      userBehavior.genrePreferences.has(genre)
    ).length || 0;

    confidence += matchingGenres * 0.1;

    // Increase confidence based on content quality
    if (rec.content.averageRating) {
      confidence += (rec.content.averageRating - 3) * 0.1; // Boost for ratings above 3
    }

    // Increase confidence based on popularity
    if (rec.content.viewCount > 1000) {
      confidence += 0.1;
    }

    return Math.min(confidence, 1.0);
  }

  async generateSimilarContentRecommendations(
    contentId: string,
    profileId: string,
    limit: number = 5,
  ): Promise<any[]> {
    try {
      const baseContent = await this.prisma.content.findUnique({
        where: { id: contentId },
      });

      if (!baseContent) {
        throw new Error('Content not found');
      }

      // Find similar content based on genres and tags
      const similarContent = await this.prisma.content.findMany({
        where: {
          AND: [
            { id: { not: contentId } },
            { status: 'PUBLISHED' },
            {
              OR: [
                {
                  genres: {
                    hasSome: baseContent.genres,
                  },
                },
                {
                  tags: {
                    hasSome: baseContent.tags,
                  },
                },
              ],
            },
          ],
        },
        take: limit * 2, // Get more to filter and rank
        orderBy: { averageRating: 'desc' },
      });

      // Calculate similarity scores and generate reasons
      const recommendations = await Promise.all(
        similarContent.slice(0, limit).map(async (content) => {
          const similarityScore = this.calculateSimilarityScore(baseContent, content);
          const reason = `Because you watched "${baseContent.title}"`;

          return {
            content,
            reason,
            score: similarityScore,
            type: RecommendationType.SIMILAR,
          };
        }),
      );

      // Save recommendations
      await this.saveRecommendations(profileId, recommendations);

      return recommendations.sort((a, b) => b.score - a.score);
    } catch (error) {
      this.logger.error('Error generating similar content recommendations:', error);
      throw error;
    }
  }

  async getTrendingRecommendations(limit: number = 10): Promise<any[]> {
    try {
      // Get trending content based on recent views and ratings
      const trendingContent = await this.prisma.content.findMany({
        where: {
          status: 'PUBLISHED',
          publishedAt: {
            gte: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000), // Last 30 days
          },
        },
        orderBy: [
          { viewCount: 'desc' },
          { averageRating: 'desc' },
        ],
        take: limit,
      });

      return trendingContent.map(content => ({
        content,
        reason: 'Trending now',
        score: content.viewCount * (content.averageRating || 0),
        type: RecommendationType.TRENDING,
      }));
    } catch (error) {
      this.logger.error('Error getting trending recommendations:', error);
      throw error;
    }
  }

  async getContinueWatchingRecommendations(profileId: string): Promise<any[]> {
    try {
      const incompleteWatches = await this.prisma.watchHistory.findMany({
        where: {
          profileId,
          completed: false,
          progress: { gt: 5 }, // At least 5% watched
        },
        include: { content: true, episode: true },
        orderBy: { watchedAt: 'desc' },
        take: 10,
      });

      return incompleteWatches.map(watch => ({
        content: watch.content,
        episode: watch.episode,
        progress: watch.progress,
        reason: 'Continue watching',
        score: 100 - watch.progress, // Higher score for less watched content
        type: RecommendationType.CONTINUE_WATCHING,
      }));
    } catch (error) {
      this.logger.error('Error getting continue watching recommendations:', error);
      throw error;
    }
  }

  private async getContentByPreferences(
    genres: string[],
    excludeIds: string[],
    limit: number,
  ): Promise<any[]> {
    return this.prisma.content.findMany({
      where: {
        AND: [
          { status: 'PUBLISHED' },
          { id: { notIn: excludeIds } },
          {
            genres: {
              hasSome: genres,
            },
          },
        ],
      },
      orderBy: [
        { averageRating: 'desc' },
        { viewCount: 'desc' },
      ],
      take: limit,
    });
  }

  private async generateRecommendationReason(
    content: any,
    userPreferences: string[],
  ): Promise<string> {
    const matchingPreferences = userPreferences.filter(pref =>
      content.genres.includes(pref) || content.tags.includes(pref),
    );

    if (matchingPreferences.length > 0) {
      return `Based on your interest in ${matchingPreferences.slice(0, 2).join(' and ')}`;
    }

    return 'Recommended for you';
  }

  private calculateRecommendationScore(content: any, userActions: any[]): number {
    let score = 0;

    // Base score from content quality
    score += (content.averageRating || 0) * 10;
    score += Math.log(content.viewCount + 1) * 5;

    // Bonus for matching user preferences
    const userGenres = userActions
      .filter(action => action.type === 'favorite' || action.type === 'rating')
      .flatMap(action => action.content.genres);

    const genreMatches = content.genres.filter(genre => userGenres.includes(genre)).length;
    score += genreMatches * 15;

    return score;
  }

  private calculateSimilarityScore(baseContent: any, compareContent: any): number {
    let score = 0;

    // Genre similarity
    const genreOverlap = baseContent.genres.filter(genre =>
      compareContent.genres.includes(genre),
    ).length;
    score += genreOverlap * 20;

    // Tag similarity
    const tagOverlap = baseContent.tags.filter(tag =>
      compareContent.tags.includes(tag),
    ).length;
    score += tagOverlap * 10;

    // Rating similarity
    if (baseContent.averageRating && compareContent.averageRating) {
      const ratingDiff = Math.abs(baseContent.averageRating - compareContent.averageRating);
      score += (5 - ratingDiff) * 5;
    }

    return score;
  }

  private async saveRecommendations(profileId: string, recommendations: any[]): Promise<void> {
    try {
      // Delete old recommendations of the same type
      const types = [...new Set(recommendations.map(r => r.type))];
      await this.prisma.recommendation.deleteMany({
        where: {
          profileId,
          type: { in: types },
        },
      });

      // Save new recommendations
      await this.prisma.recommendation.createMany({
        data: recommendations.map(rec => ({
          profileId,
          contentId: rec.content.id,
          type: rec.type,
          score: rec.score,
          reason: rec.reason,
          expiresAt: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000), // 7 days
        })),
      });
    } catch (error) {
      this.logger.error('Error saving recommendations:', error);
    }
  }
}
