import { Injectable, Logger } from '@nestjs/common';
import { PrismaService } from '../../common/prisma/prisma.service';
import { AiService } from '../ai.service';
import { RecommendationType } from '@prisma/client';

@Injectable()
export class RecommendationService {
  private readonly logger = new Logger(RecommendationService.name);

  constructor(
    private readonly prisma: PrismaService,
    private readonly aiService: AiService,
  ) {}

  async generatePersonalizedRecommendations(
    profileId: string,
    limit: number = 10,
  ): Promise<any[]> {
    try {
      // Get user's watch history and preferences
      const profile = await this.prisma.profile.findUnique({
        where: { id: profileId },
        include: {
          watchHistory: {
            include: { content: true },
            orderBy: { watchedAt: 'desc' },
            take: 50,
          },
          favorites: {
            include: { content: true },
          },
          ratings: {
            include: { content: true },
            where: { rating: { gte: 4 } },
          },
        },
      });

      if (!profile) {
        throw new Error('Profile not found');
      }

      // Analyze user behavior
      const userActions = [
        ...profile.watchHistory.map(wh => ({
          type: 'watch',
          content: wh.content,
          progress: wh.progress,
          completed: wh.completed,
        })),
        ...profile.favorites.map(fav => ({
          type: 'favorite',
          content: fav.content,
        })),
        ...profile.ratings.map(rating => ({
          type: 'rating',
          content: rating.content,
          rating: rating.rating,
        })),
      ];

      const behaviorAnalysis = await this.aiService.analyzeUserBehavior(userActions);

      // Get content based on preferences
      const recommendedContent = await this.getContentByPreferences(
        behaviorAnalysis.recommendedGenres,
        profile.watchHistory.map(wh => wh.contentId),
        limit,
      );

      // Generate AI-powered recommendations with reasons
      const recommendations = await Promise.all(
        recommendedContent.map(async (content) => {
          const reason = await this.generateRecommendationReason(
            content,
            behaviorAnalysis.preferences,
          );

          return {
            content,
            reason,
            score: this.calculateRecommendationScore(content, userActions),
            type: RecommendationType.PERSONALIZED,
          };
        }),
      );

      // Save recommendations to database
      await this.saveRecommendations(profileId, recommendations);

      return recommendations.sort((a, b) => b.score - a.score);
    } catch (error) {
      this.logger.error('Error generating personalized recommendations:', error);
      throw error;
    }
  }

  async generateSimilarContentRecommendations(
    contentId: string,
    profileId: string,
    limit: number = 5,
  ): Promise<any[]> {
    try {
      const baseContent = await this.prisma.content.findUnique({
        where: { id: contentId },
      });

      if (!baseContent) {
        throw new Error('Content not found');
      }

      // Find similar content based on genres and tags
      const similarContent = await this.prisma.content.findMany({
        where: {
          AND: [
            { id: { not: contentId } },
            { status: 'PUBLISHED' },
            {
              OR: [
                {
                  genres: {
                    hasSome: baseContent.genres,
                  },
                },
                {
                  tags: {
                    hasSome: baseContent.tags,
                  },
                },
              ],
            },
          ],
        },
        take: limit * 2, // Get more to filter and rank
        orderBy: { averageRating: 'desc' },
      });

      // Calculate similarity scores and generate reasons
      const recommendations = await Promise.all(
        similarContent.slice(0, limit).map(async (content) => {
          const similarityScore = this.calculateSimilarityScore(baseContent, content);
          const reason = `Because you watched "${baseContent.title}"`;

          return {
            content,
            reason,
            score: similarityScore,
            type: RecommendationType.SIMILAR,
          };
        }),
      );

      // Save recommendations
      await this.saveRecommendations(profileId, recommendations);

      return recommendations.sort((a, b) => b.score - a.score);
    } catch (error) {
      this.logger.error('Error generating similar content recommendations:', error);
      throw error;
    }
  }

  async getTrendingRecommendations(limit: number = 10): Promise<any[]> {
    try {
      // Get trending content based on recent views and ratings
      const trendingContent = await this.prisma.content.findMany({
        where: {
          status: 'PUBLISHED',
          publishedAt: {
            gte: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000), // Last 30 days
          },
        },
        orderBy: [
          { viewCount: 'desc' },
          { averageRating: 'desc' },
        ],
        take: limit,
      });

      return trendingContent.map(content => ({
        content,
        reason: 'Trending now',
        score: content.viewCount * (content.averageRating || 0),
        type: RecommendationType.TRENDING,
      }));
    } catch (error) {
      this.logger.error('Error getting trending recommendations:', error);
      throw error;
    }
  }

  async getContinueWatchingRecommendations(profileId: string): Promise<any[]> {
    try {
      const incompleteWatches = await this.prisma.watchHistory.findMany({
        where: {
          profileId,
          completed: false,
          progress: { gt: 5 }, // At least 5% watched
        },
        include: { content: true, episode: true },
        orderBy: { watchedAt: 'desc' },
        take: 10,
      });

      return incompleteWatches.map(watch => ({
        content: watch.content,
        episode: watch.episode,
        progress: watch.progress,
        reason: 'Continue watching',
        score: 100 - watch.progress, // Higher score for less watched content
        type: RecommendationType.CONTINUE_WATCHING,
      }));
    } catch (error) {
      this.logger.error('Error getting continue watching recommendations:', error);
      throw error;
    }
  }

  private async getContentByPreferences(
    genres: string[],
    excludeIds: string[],
    limit: number,
  ): Promise<any[]> {
    return this.prisma.content.findMany({
      where: {
        AND: [
          { status: 'PUBLISHED' },
          { id: { notIn: excludeIds } },
          {
            genres: {
              hasSome: genres,
            },
          },
        ],
      },
      orderBy: [
        { averageRating: 'desc' },
        { viewCount: 'desc' },
      ],
      take: limit,
    });
  }

  private async generateRecommendationReason(
    content: any,
    userPreferences: string[],
  ): Promise<string> {
    const matchingPreferences = userPreferences.filter(pref =>
      content.genres.includes(pref) || content.tags.includes(pref),
    );

    if (matchingPreferences.length > 0) {
      return `Based on your interest in ${matchingPreferences.slice(0, 2).join(' and ')}`;
    }

    return 'Recommended for you';
  }

  private calculateRecommendationScore(content: any, userActions: any[]): number {
    let score = 0;

    // Base score from content quality
    score += (content.averageRating || 0) * 10;
    score += Math.log(content.viewCount + 1) * 5;

    // Bonus for matching user preferences
    const userGenres = userActions
      .filter(action => action.type === 'favorite' || action.type === 'rating')
      .flatMap(action => action.content.genres);

    const genreMatches = content.genres.filter(genre => userGenres.includes(genre)).length;
    score += genreMatches * 15;

    return score;
  }

  private calculateSimilarityScore(baseContent: any, compareContent: any): number {
    let score = 0;

    // Genre similarity
    const genreOverlap = baseContent.genres.filter(genre =>
      compareContent.genres.includes(genre),
    ).length;
    score += genreOverlap * 20;

    // Tag similarity
    const tagOverlap = baseContent.tags.filter(tag =>
      compareContent.tags.includes(tag),
    ).length;
    score += tagOverlap * 10;

    // Rating similarity
    if (baseContent.averageRating && compareContent.averageRating) {
      const ratingDiff = Math.abs(baseContent.averageRating - compareContent.averageRating);
      score += (5 - ratingDiff) * 5;
    }

    return score;
  }

  private async saveRecommendations(profileId: string, recommendations: any[]): Promise<void> {
    try {
      // Delete old recommendations of the same type
      const types = [...new Set(recommendations.map(r => r.type))];
      await this.prisma.recommendation.deleteMany({
        where: {
          profileId,
          type: { in: types },
        },
      });

      // Save new recommendations
      await this.prisma.recommendation.createMany({
        data: recommendations.map(rec => ({
          profileId,
          contentId: rec.content.id,
          type: rec.type,
          score: rec.score,
          reason: rec.reason,
          expiresAt: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000), // 7 days
        })),
      });
    } catch (error) {
      this.logger.error('Error saving recommendations:', error);
    }
  }
}
