import { ApiProperty } from '@nestjs/swagger';
import { IsString, IsOptional } from 'class-validator';

export class GenerateSubtitlesDto {
  @ApiProperty({
    description: 'Path to the audio file',
    example: '/uploads/audio/content-123.mp3',
  })
  @IsString()
  audioFilePath: string;

  @ApiProperty({
    description: 'Content ID to associate subtitles with',
    example: 'content-123',
  })
  @IsString()
  contentId: string;

  @ApiProperty({
    description: 'Language code for the subtitles',
    example: 'en',
    required: false,
  })
  @IsOptional()
  @IsString()
  language?: string = 'en';
}
