import { create } from 'zustand';
import { VideoPlayerState, Content, Episode, WatchProgress } from '@/types';

interface VideoStore {
  // Player state
  playerState: VideoPlayerState;
  
  // Current content
  currentContent: Content | null;
  currentEpisode: Episode | null;
  
  // Watch progress
  watchProgress: Record<string, WatchProgress>;
  
  // Player settings
  autoplay: boolean;
  autoNext: boolean;
  skipIntro: boolean;
  skipCredits: boolean;
  
  // Actions
  setPlayerState: (state: Partial<VideoPlayerState>) => void;
  setCurrentContent: (content: Content | null) => void;
  setCurrentEpisode: (episode: Episode | null) => void;
  updateWatchProgress: (contentId: string, progress: WatchProgress) => void;
  setAutoplay: (autoplay: boolean) => void;
  setAutoNext: (autoNext: boolean) => void;
  setSkipIntro: (skipIntro: boolean) => void;
  setSkipCredits: (skipCredits: boolean) => void;
  resetPlayer: () => void;
}

const initialPlayerState: VideoPlayerState = {
  isPlaying: false,
  currentTime: 0,
  duration: 0,
  volume: 1,
  isMuted: false,
  isFullscreen: false,
  quality: 'auto',
  playbackRate: 1,
};

export const useVideoStore = create<VideoStore>((set, get) => ({
  // Initial state
  playerState: initialPlayerState,
  currentContent: null,
  currentEpisode: null,
  watchProgress: {},
  autoplay: true,
  autoNext: true,
  skipIntro: true,
  skipCredits: false,

  // Actions
  setPlayerState: (newState) => set(state => ({
    playerState: { ...state.playerState, ...newState }
  })),

  setCurrentContent: (content) => set({ currentContent: content }),

  setCurrentEpisode: (episode) => set({ currentEpisode: episode }),

  updateWatchProgress: (contentId, progress) => set(state => ({
    watchProgress: {
      ...state.watchProgress,
      [contentId]: progress,
    }
  })),

  setAutoplay: (autoplay) => set({ autoplay }),

  setAutoNext: (autoNext) => set({ autoNext }),

  setSkipIntro: (skipIntro) => set({ skipIntro }),

  setSkipCredits: (skipCredits) => set({ skipCredits }),

  resetPlayer: () => set({
    playerState: initialPlayerState,
    currentContent: null,
    currentEpisode: null,
  }),
}));
