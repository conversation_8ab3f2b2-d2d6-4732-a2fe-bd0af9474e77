// Mock API client for demo purposes
export const apiClient = {
  auth: {
    login: async (email: string, password: string) => {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      return {
        token: 'demo-token',
        user: {
          id: '1',
          email,
          name: 'Demo User',
          subscription: 'PREMIUM' as const,
          role: 'USER' as const,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
        },
        profiles: [
          {
            id: '1',
            name: 'Main Profile',
            isKid: false,
            language: 'en',
            maturityLevel: 'ADULT',
            userId: '1',
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString(),
          }
        ]
      };
    },
    
    register: async (data: { name: string; email: string; password: string }) => {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      return {
        token: 'demo-token',
        user: {
          id: '1',
          email: data.email,
          name: data.name,
          subscription: 'FREE' as const,
          role: 'USER' as const,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
        },
        profiles: []
      };
    },
  },
};
