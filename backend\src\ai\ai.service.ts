import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import OpenAI from 'openai';

@Injectable()
export class AiService {
  private readonly logger = new Logger(AiService.name);
  private readonly openai: OpenAI;

  constructor(private readonly configService: ConfigService) {
    const apiKey = this.configService.get('OPENAI_API_KEY');
    if (!apiKey) {
      this.logger.warn('OpenAI API key not configured');
      return;
    }

    this.openai = new OpenAI({
      apiKey,
    });
  }

  async generateText(prompt: string, options?: {
    model?: string;
    maxTokens?: number;
    temperature?: number;
  }): Promise<string> {
    if (!this.openai) {
      throw new Error('OpenAI not configured');
    }

    try {
      const response = await this.openai.chat.completions.create({
        model: options?.model || this.configService.get('OPENAI_MODEL', 'gpt-4'),
        messages: [
          {
            role: 'user',
            content: prompt,
          },
        ],
        max_tokens: options?.maxTokens || 1000,
        temperature: options?.temperature || 0.7,
      });

      return response.choices[0]?.message?.content || '';
    } catch (error) {
      this.logger.error('Error generating text with OpenAI:', error);
      throw error;
    }
  }

  async generateContentSummary(title: string, description: string): Promise<string> {
    const prompt = `
      Create a compelling and concise summary for the following content:
      
      Title: ${title}
      Description: ${description}
      
      Generate a 2-3 sentence summary that would entice viewers to watch this content.
      Focus on the main plot points, genre, and what makes it unique or interesting.
    `;

    return this.generateText(prompt, { maxTokens: 200 });
  }

  async generateContentTags(title: string, description: string, genre: string[]): Promise<string[]> {
    const prompt = `
      Based on the following content information, generate 5-10 relevant tags that would help with content discovery:
      
      Title: ${title}
      Description: ${description}
      Genres: ${genre.join(', ')}
      
      Return only the tags as a comma-separated list, focusing on themes, mood, target audience, and key elements.
    `;

    const response = await this.generateText(prompt, { maxTokens: 100 });
    return response.split(',').map(tag => tag.trim()).filter(tag => tag.length > 0);
  }

  async transcribeAudio(audioBuffer: Buffer): Promise<string> {
    if (!this.openai) {
      throw new Error('OpenAI not configured');
    }

    try {
      // Create a temporary file for the audio
      const file = new File([audioBuffer], 'audio.mp3', { type: 'audio/mpeg' });
      
      const response = await this.openai.audio.transcriptions.create({
        file,
        model: 'whisper-1',
        language: 'en', // Can be made configurable
      });

      return response.text;
    } catch (error) {
      this.logger.error('Error transcribing audio with Whisper:', error);
      throw error;
    }
  }

  async translateText(text: string, targetLanguage: string): Promise<string> {
    const prompt = `
      Translate the following text to ${targetLanguage}:
      
      "${text}"
      
      Provide only the translation, maintaining the original tone and context.
    `;

    return this.generateText(prompt, { maxTokens: text.length * 2 });
  }

  async analyzeUserBehavior(userActions: any[]): Promise<{
    preferences: string[];
    recommendedGenres: string[];
    viewingPatterns: string;
  }> {
    const prompt = `
      Analyze the following user behavior data and provide insights:
      
      User Actions: ${JSON.stringify(userActions, null, 2)}
      
      Based on this data, provide:
      1. Top 5 content preferences
      2. Top 3 recommended genres
      3. A brief description of viewing patterns
      
      Format the response as JSON with keys: preferences, recommendedGenres, viewingPatterns
    `;

    const response = await this.generateText(prompt, { maxTokens: 500 });
    
    try {
      return JSON.parse(response);
    } catch (error) {
      this.logger.error('Error parsing AI response:', error);
      return {
        preferences: [],
        recommendedGenres: [],
        viewingPatterns: 'Unable to analyze patterns',
      };
    }
  }

  async generatePersonalizedDescription(
    content: any,
    userPreferences: string[],
  ): Promise<string> {
    const prompt = `
      Create a personalized description for this content based on user preferences:
      
      Content: ${content.title} - ${content.description}
      User Preferences: ${userPreferences.join(', ')}
      
      Generate a 1-2 sentence description that highlights aspects most relevant to this user's preferences.
    `;

    return this.generateText(prompt, { maxTokens: 150 });
  }

  async moderateContent(text: string): Promise<{
    flagged: boolean;
    categories: string[];
    confidence: number;
  }> {
    if (!this.openai) {
      throw new Error('OpenAI not configured');
    }

    try {
      const response = await this.openai.moderations.create({
        input: text,
      });

      const result = response.results[0];
      const flaggedCategories = Object.entries(result.categories)
        .filter(([_, flagged]) => flagged)
        .map(([category]) => category);

      return {
        flagged: result.flagged,
        categories: flaggedCategories,
        confidence: Math.max(...Object.values(result.category_scores)),
      };
    } catch (error) {
      this.logger.error('Error moderating content:', error);
      throw error;
    }
  }
}
