'use client';

import React, { useEffect } from 'react';
import Head from 'next/head';
import { useRouter } from 'next/router';
import { useAuthStore } from '@/store/auth.store';

export default function Home() {
  const router = useRouter();
  const { isAuthenticated, currentProfile } = useAuthStore();

  useEffect(() => {
    if (!isAuthenticated) {
      router.push('/auth/login');
    } else if (!currentProfile) {
      router.push('/profiles');
    } else {
      router.push('/dashboard');
    }
  }, [isAuthenticated, currentProfile, router]);

  return (
    <div>
      <Head>
        <title>HyperFlix - Stream Movies & TV Shows</title>
        <meta name="description" content="Watch unlimited movies and TV shows with HyperFlix" />
      </Head>

      <main className="min-h-screen bg-black text-white flex items-center justify-center">
        <div className="text-center">
          <div className="w-16 h-16 bg-red-600 rounded-lg flex items-center justify-center mx-auto mb-4">
            <span className="text-white font-bold text-2xl">H</span>
          </div>
          <h1 className="text-4xl font-bold mb-4">HyperFlix</h1>
          <p className="text-gray-400 mb-8">Loading...</p>
          <div className="w-8 h-8 border-4 border-gray-300 border-t-red-600 rounded-full animate-spin mx-auto"></div>
        </div>
      </main>
    </div>
  );
}
