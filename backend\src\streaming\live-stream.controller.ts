import { <PERSON>, Get, Post, Body, Param, UseGuards } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth } from '@nestjs/swagger';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';

@ApiTags('live-streaming')
@Controller('live-streaming')
export class LiveStreamController {
  @Get()
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth('JWT-auth')
  @ApiOperation({ summary: 'Get live streams' })
  @ApiResponse({ status: 200, description: 'List of live streams' })
  async getLiveStreams() {
    return { streams: [] };
  }

  @Post()
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth('JWT-auth')
  @ApiOperation({ summary: 'Create live stream' })
  @ApiResponse({ status: 201, description: 'Live stream created' })
  async createLiveStream(@Body() createStreamDto: any) {
    return { message: 'Live stream created' };
  }
}
