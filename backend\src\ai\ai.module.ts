import { Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';

// Controllers
import { AiController } from './ai.controller';

// Services
import { AiService } from './ai.service';
import { RecommendationService } from './services/recommendation.service';
import { SubtitleService } from './services/subtitle.service';
import { SearchService } from './services/search.service';
import { AnalyticsService } from './services/analytics.service';

// Modules
import { ContentModule } from '../content/content.module';
import { UsersModule } from '../users/users.module';

@Module({
  imports: [
    ConfigModule,
    ContentModule,
    UsersModule,
  ],
  controllers: [AiController],
  providers: [
    AiService,
    RecommendationService,
    SubtitleService,
    SearchService,
    AnalyticsService,
  ],
  exports: [
    AiService,
    RecommendationService,
    SubtitleService,
    SearchService,
    AnalyticsService,
  ],
})
export class AiModule {}
