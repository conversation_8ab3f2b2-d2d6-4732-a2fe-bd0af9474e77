import { Injectable, Logger } from '@nestjs/common';
import { PrismaService } from '../../common/prisma/prisma.service';

@Injectable()
export class AnalyticsService {
  private readonly logger = new Logger(AnalyticsService.name);

  constructor(private readonly prisma: PrismaService) {}

  async trackStreamStart(
    contentId: string,
    userId: string,
    profileId: string,
    quality: string,
    deviceInfo?: any,
  ): Promise<void> {
    try {
      await this.prisma.viewAnalytics.create({
        data: {
          contentId,
          userId,
          profileId,
          sessionId: this.generateSessionId(),
          quality,
          deviceType: deviceInfo?.type || 'unknown',
          platform: deviceInfo?.platform || 'web',
          country: deviceInfo?.country,
          region: deviceInfo?.region,
          startTime: new Date(),
        },
      });

      this.logger.debug(`Tracked stream start for content ${contentId}, user ${userId}`);
    } catch (error) {
      this.logger.error('Error tracking stream start:', error);
    }
  }

  async trackEpisodeStreamStart(
    episodeId: string,
    userId: string,
    profileId: string,
    quality: string,
    deviceInfo?: any,
  ): Promise<void> {
    try {
      await this.prisma.viewAnalytics.create({
        data: {
          episodeId,
          userId,
          profileId,
          sessionId: this.generateSessionId(),
          quality,
          deviceType: deviceInfo?.type || 'unknown',
          platform: deviceInfo?.platform || 'web',
          country: deviceInfo?.country,
          region: deviceInfo?.region,
          startTime: new Date(),
        },
      });

      this.logger.debug(`Tracked episode stream start for episode ${episodeId}, user ${userId}`);
    } catch (error) {
      this.logger.error('Error tracking episode stream start:', error);
    }
  }

  async trackWatchProgress(
    contentId: string,
    userId: string,
    profileId: string,
    progress: number,
    episodeId?: string,
    duration?: number,
  ): Promise<void> {
    try {
      // Find the most recent analytics record for this session
      const whereClause = episodeId
        ? { episodeId, userId, profileId }
        : { contentId, userId, profileId };

      const analyticsRecord = await this.prisma.viewAnalytics.findFirst({
        where: {
          ...whereClause,
          endTime: null,
        },
        orderBy: { startTime: 'desc' },
      });

      if (analyticsRecord) {
        await this.prisma.viewAnalytics.update({
          where: { id: analyticsRecord.id },
          data: {
            progress,
            duration,
            endTime: progress >= 90 ? new Date() : null, // Mark as ended if 90% watched
          },
        });
      }

      this.logger.debug(`Tracked watch progress: ${progress}% for content ${contentId}`);
    } catch (error) {
      this.logger.error('Error tracking watch progress:', error);
    }
  }

  async trackStreamEnd(
    contentId: string,
    userId: string,
    profileId: string,
    finalProgress: number,
    totalDuration: number,
    episodeId?: string,
  ): Promise<void> {
    try {
      const whereClause = episodeId
        ? { episodeId, userId, profileId }
        : { contentId, userId, profileId };

      const analyticsRecord = await this.prisma.viewAnalytics.findFirst({
        where: {
          ...whereClause,
          endTime: null,
        },
        orderBy: { startTime: 'desc' },
      });

      if (analyticsRecord) {
        await this.prisma.viewAnalytics.update({
          where: { id: analyticsRecord.id },
          data: {
            endTime: new Date(),
            progress: finalProgress,
            duration: totalDuration,
          },
        });
      }

      this.logger.debug(`Tracked stream end for content ${contentId}, final progress: ${finalProgress}%`);
    } catch (error) {
      this.logger.error('Error tracking stream end:', error);
    }
  }

  async trackUserInteraction(
    contentId: string,
    userId: string,
    profileId: string,
    interactionType: 'pause' | 'seek' | 'quality_change' | 'fullscreen' | 'volume_change',
    metadata?: any,
  ): Promise<void> {
    try {
      const analyticsRecord = await this.prisma.viewAnalytics.findFirst({
        where: {
          contentId,
          userId,
          profileId,
          endTime: null,
        },
        orderBy: { startTime: 'desc' },
      });

      if (analyticsRecord) {
        const currentData = analyticsRecord as any;
        
        switch (interactionType) {
          case 'pause':
            await this.prisma.viewAnalytics.update({
              where: { id: analyticsRecord.id },
              data: { paused: (currentData.paused || 0) + 1 },
            });
            break;
          case 'seek':
            await this.prisma.viewAnalytics.update({
              where: { id: analyticsRecord.id },
              data: { seeked: (currentData.seeked || 0) + 1 },
            });
            break;
          default:
            // Log other interactions
            this.logger.debug(`User interaction: ${interactionType} for content ${contentId}`);
        }
      }
    } catch (error) {
      this.logger.error('Error tracking user interaction:', error);
    }
  }

  async getContentAnalytics(
    contentId: string,
    timeRange: string = '7d',
  ): Promise<{
    totalViews: number;
    uniqueViewers: number;
    averageWatchTime: number;
    completionRate: number;
    topCountries: Array<{ country: string; views: number }>;
    deviceBreakdown: Array<{ device: string; views: number }>;
    qualityDistribution: Array<{ quality: string; views: number }>;
  }> {
    try {
      const startDate = this.getStartDateFromRange(timeRange);

      const analytics = await this.prisma.viewAnalytics.findMany({
        where: {
          contentId,
          startTime: {
            gte: startDate,
          },
        },
      });

      const totalViews = analytics.length;
      const uniqueViewers = new Set(analytics.map(a => a.userId)).size;
      
      const watchTimes = analytics
        .filter(a => a.duration)
        .map(a => a.duration!);
      const averageWatchTime = watchTimes.length > 0
        ? watchTimes.reduce((sum, time) => sum + time, 0) / watchTimes.length
        : 0;

      const completedViews = analytics.filter(a => a.progress && a.progress >= 90).length;
      const completionRate = totalViews > 0 ? (completedViews / totalViews) * 100 : 0;

      // Top countries
      const countryMap = new Map<string, number>();
      analytics.forEach(a => {
        if (a.country) {
          countryMap.set(a.country, (countryMap.get(a.country) || 0) + 1);
        }
      });
      const topCountries = Array.from(countryMap.entries())
        .map(([country, views]) => ({ country, views }))
        .sort((a, b) => b.views - a.views)
        .slice(0, 10);

      // Device breakdown
      const deviceMap = new Map<string, number>();
      analytics.forEach(a => {
        if (a.deviceType) {
          deviceMap.set(a.deviceType, (deviceMap.get(a.deviceType) || 0) + 1);
        }
      });
      const deviceBreakdown = Array.from(deviceMap.entries())
        .map(([device, views]) => ({ device, views }))
        .sort((a, b) => b.views - a.views);

      // Quality distribution
      const qualityMap = new Map<string, number>();
      analytics.forEach(a => {
        if (a.quality) {
          qualityMap.set(a.quality, (qualityMap.get(a.quality) || 0) + 1);
        }
      });
      const qualityDistribution = Array.from(qualityMap.entries())
        .map(([quality, views]) => ({ quality, views }))
        .sort((a, b) => b.views - a.views);

      return {
        totalViews,
        uniqueViewers,
        averageWatchTime,
        completionRate,
        topCountries,
        deviceBreakdown,
        qualityDistribution,
      };
    } catch (error) {
      this.logger.error(`Error getting content analytics for ${contentId}:`, error);
      throw error;
    }
  }

  async getUserAnalytics(
    userId: string,
    timeRange: string = '30d',
  ): Promise<{
    totalWatchTime: number;
    contentWatched: number;
    favoriteGenres: Array<{ genre: string; count: number }>;
    watchingPatterns: {
      hourlyDistribution: Array<{ hour: number; count: number }>;
      dailyDistribution: Array<{ day: string; count: number }>;
    };
    deviceUsage: Array<{ device: string; usage: number }>;
  }> {
    try {
      const startDate = this.getStartDateFromRange(timeRange);

      const analytics = await this.prisma.viewAnalytics.findMany({
        where: {
          userId,
          startTime: {
            gte: startDate,
          },
        },
      });

      const totalWatchTime = analytics
        .filter(a => a.duration)
        .reduce((sum, a) => sum + a.duration!, 0);

      const contentWatched = new Set(
        analytics.map(a => a.contentId || a.episodeId).filter(Boolean)
      ).size;

      // Favorite genres (would need to join with content table)
      const favoriteGenres: Array<{ genre: string; count: number }> = [];

      // Hourly distribution
      const hourlyMap = new Map<number, number>();
      analytics.forEach(a => {
        const hour = a.startTime.getHours();
        hourlyMap.set(hour, (hourlyMap.get(hour) || 0) + 1);
      });
      const hourlyDistribution = Array.from(hourlyMap.entries())
        .map(([hour, count]) => ({ hour, count }))
        .sort((a, b) => a.hour - b.hour);

      // Daily distribution
      const dailyMap = new Map<string, number>();
      analytics.forEach(a => {
        const day = a.startTime.toLocaleDateString('en-US', { weekday: 'long' });
        dailyMap.set(day, (dailyMap.get(day) || 0) + 1);
      });
      const dailyDistribution = Array.from(dailyMap.entries())
        .map(([day, count]) => ({ day, count }));

      // Device usage
      const deviceMap = new Map<string, number>();
      analytics.forEach(a => {
        if (a.deviceType) {
          deviceMap.set(a.deviceType, (deviceMap.get(a.deviceType) || 0) + 1);
        }
      });
      const deviceUsage = Array.from(deviceMap.entries())
        .map(([device, usage]) => ({ device, usage }))
        .sort((a, b) => b.usage - a.usage);

      return {
        totalWatchTime,
        contentWatched,
        favoriteGenres,
        watchingPatterns: {
          hourlyDistribution,
          dailyDistribution,
        },
        deviceUsage,
      };
    } catch (error) {
      this.logger.error(`Error getting user analytics for ${userId}:`, error);
      throw error;
    }
  }

  async getPlatformAnalytics(timeRange: string = '7d'): Promise<{
    totalStreams: number;
    totalWatchTime: number;
    activeUsers: number;
    topContent: Array<{ contentId: string; title: string; views: number }>;
    platformMetrics: {
      averageSessionDuration: number;
      bounceRate: number;
      peakConcurrentUsers: number;
    };
  }> {
    try {
      const startDate = this.getStartDateFromRange(timeRange);

      const analytics = await this.prisma.viewAnalytics.findMany({
        where: {
          startTime: {
            gte: startDate,
          },
        },
      });

      const totalStreams = analytics.length;
      const totalWatchTime = analytics
        .filter(a => a.duration)
        .reduce((sum, a) => sum + a.duration!, 0);
      const activeUsers = new Set(analytics.map(a => a.userId)).size;

      // Top content (would need to join with content table for titles)
      const contentMap = new Map<string, number>();
      analytics.forEach(a => {
        if (a.contentId) {
          contentMap.set(a.contentId, (contentMap.get(a.contentId) || 0) + 1);
        }
      });
      const topContent = Array.from(contentMap.entries())
        .map(([contentId, views]) => ({ contentId, title: 'Content Title', views }))
        .sort((a, b) => b.views - a.views)
        .slice(0, 10);

      // Platform metrics
      const sessions = analytics.filter(a => a.duration);
      const averageSessionDuration = sessions.length > 0
        ? sessions.reduce((sum, s) => sum + s.duration!, 0) / sessions.length
        : 0;

      const shortSessions = sessions.filter(s => s.duration! < 300).length; // Less than 5 minutes
      const bounceRate = sessions.length > 0 ? (shortSessions / sessions.length) * 100 : 0;

      // Peak concurrent users (simplified calculation)
      const peakConcurrentUsers = Math.max(...Array.from(
        new Map(analytics.map(a => [
          Math.floor(a.startTime.getTime() / (1000 * 60 * 15)), // 15-minute intervals
          1
        ])).values()
      ));

      return {
        totalStreams,
        totalWatchTime,
        activeUsers,
        topContent,
        platformMetrics: {
          averageSessionDuration,
          bounceRate,
          peakConcurrentUsers,
        },
      };
    } catch (error) {
      this.logger.error('Error getting platform analytics:', error);
      throw error;
    }
  }

  private generateSessionId(): string {
    return `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  private getStartDateFromRange(timeRange: string): Date {
    const now = new Date();
    const ranges: Record<string, number> = {
      '1h': 1,
      '24h': 24,
      '7d': 24 * 7,
      '30d': 24 * 30,
      '90d': 24 * 90,
    };

    const hours = ranges[timeRange] || 24 * 7; // Default to 7 days
    return new Date(now.getTime() - hours * 60 * 60 * 1000);
  }
}
