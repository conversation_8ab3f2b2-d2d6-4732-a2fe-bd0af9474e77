import {
  Controller,
  Get,
  Post,
  Body,
  Param,
  Query,
  UseGuards,
  UploadedFile,
  UseInterceptors,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
  ApiConsumes,
  ApiBody,
} from '@nestjs/swagger';
import { FileInterceptor } from '@nestjs/platform-express';
import { User } from '@prisma/client';

// Services
import { AiService } from './ai.service';
import { RecommendationService } from './services/recommendation.service';
import { SubtitleService } from './services/subtitle.service';
import { SearchService } from './services/search.service';

// Guards
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';

// Decorators
import { CurrentUser } from '../auth/decorators/current-user.decorator';
import { Public } from '../auth/decorators/public.decorator';

// DTOs
import { SearchDto } from './dto/search.dto';
import { GenerateSubtitlesDto } from './dto/generate-subtitles.dto';

@ApiTags('ai')
@Controller('ai')
export class AiController {
  constructor(
    private readonly aiService: AiService,
    private readonly recommendationService: RecommendationService,
    private readonly subtitleService: SubtitleService,
    private readonly searchService: SearchService,
  ) {}

  // Recommendations
  @Get('recommendations/:profileId')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth('JWT-auth')
  @ApiOperation({ summary: 'Get personalized recommendations for a profile' })
  @ApiResponse({ status: 200, description: 'Personalized recommendations' })
  async getPersonalizedRecommendations(
    @Param('profileId') profileId: string,
    @Query('limit') limit: number = 10,
  ) {
    return this.recommendationService.generatePersonalizedRecommendations(profileId, limit);
  }

  @Get('recommendations/similar/:contentId')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth('JWT-auth')
  @ApiOperation({ summary: 'Get similar content recommendations' })
  @ApiResponse({ status: 200, description: 'Similar content recommendations' })
  async getSimilarRecommendations(
    @Param('contentId') contentId: string,
    @CurrentUser('id') userId: string,
    @Query('profileId') profileId: string,
    @Query('limit') limit: number = 5,
  ) {
    return this.recommendationService.generateSimilarContentRecommendations(
      contentId,
      profileId,
      limit,
    );
  }

  @Get('recommendations/trending')
  @Public()
  @ApiOperation({ summary: 'Get trending content recommendations' })
  @ApiResponse({ status: 200, description: 'Trending content' })
  async getTrendingRecommendations(@Query('limit') limit: number = 10) {
    return this.recommendationService.getTrendingRecommendations(limit);
  }

  @Get('recommendations/continue-watching/:profileId')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth('JWT-auth')
  @ApiOperation({ summary: 'Get continue watching recommendations' })
  @ApiResponse({ status: 200, description: 'Continue watching recommendations' })
  async getContinueWatchingRecommendations(@Param('profileId') profileId: string) {
    return this.recommendationService.getContinueWatchingRecommendations(profileId);
  }

  // Search
  @Post('search')
  @Public()
  @ApiOperation({ summary: 'Intelligent content search' })
  @ApiResponse({ status: 200, description: 'Search results' })
  async intelligentSearch(
    @Body() searchDto: SearchDto,
    @Query('profileId') profileId?: string,
  ) {
    return this.searchService.intelligentSearch(
      searchDto.query,
      profileId,
      searchDto.filters,
      searchDto.limit,
    );
  }

  @Post('search/semantic')
  @Public()
  @ApiOperation({ summary: 'Semantic content search' })
  @ApiResponse({ status: 200, description: 'Semantic search results' })
  async semanticSearch(
    @Body() body: { query: string; limit?: number },
    @Query('profileId') profileId?: string,
  ) {
    return this.searchService.semanticSearch(body.query, profileId, body.limit);
  }

  @Get('search/suggestions')
  @Public()
  @ApiOperation({ summary: 'Get search suggestions' })
  @ApiResponse({ status: 200, description: 'Search suggestions' })
  async getSearchSuggestions(
    @Query('q') query: string,
    @Query('limit') limit: number = 5,
  ) {
    return this.searchService.searchSuggestions(query, limit);
  }

  @Post('search/voice')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth('JWT-auth')
  @UseInterceptors(FileInterceptor('audio'))
  @ApiConsumes('multipart/form-data')
  @ApiOperation({ summary: 'Voice search' })
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        audio: {
          type: 'string',
          format: 'binary',
        },
      },
    },
  })
  async voiceSearch(
    @UploadedFile() audioFile: Express.Multer.File,
    @Query('profileId') profileId?: string,
  ) {
    return this.searchService.searchByVoice(audioFile.buffer, profileId);
  }

  // Subtitles
  @Post('subtitles/generate')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth('JWT-auth')
  @ApiOperation({ summary: 'Generate subtitles from audio' })
  @ApiResponse({ status: 200, description: 'Generated subtitles' })
  async generateSubtitles(@Body() generateSubtitlesDto: GenerateSubtitlesDto) {
    return this.subtitleService.generateSubtitlesFromAudio(
      generateSubtitlesDto.audioFilePath,
      generateSubtitlesDto.contentId,
      generateSubtitlesDto.language,
    );
  }

  @Post('subtitles/translate')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth('JWT-auth')
  @ApiOperation({ summary: 'Translate subtitles' })
  @ApiResponse({ status: 200, description: 'Translated subtitles' })
  async translateSubtitles(
    @Body() body: {
      contentId: string;
      sourceLanguage: string;
      targetLanguage: string;
    },
  ) {
    return this.subtitleService.translateSubtitles(
      body.contentId,
      body.sourceLanguage,
      body.targetLanguage,
    );
  }

  @Get('subtitles/:contentId')
  @Public()
  @ApiOperation({ summary: 'Get subtitles for content' })
  @ApiResponse({ status: 200, description: 'Content subtitles' })
  async getSubtitles(
    @Param('contentId') contentId: string,
    @Query('language') language: string = 'en',
  ) {
    return this.subtitleService.getSubtitles(contentId, language);
  }

  @Get('subtitles/:contentId/languages')
  @Public()
  @ApiOperation({ summary: 'Get available subtitle languages' })
  @ApiResponse({ status: 200, description: 'Available languages' })
  async getAvailableLanguages(@Param('contentId') contentId: string) {
    return this.subtitleService.getAvailableLanguages(contentId);
  }

  // Content Enhancement
  @Post('content/summary')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth('JWT-auth')
  @ApiOperation({ summary: 'Generate content summary' })
  @ApiResponse({ status: 200, description: 'Generated summary' })
  async generateContentSummary(
    @Body() body: { title: string; description: string },
  ) {
    return this.aiService.generateContentSummary(body.title, body.description);
  }

  @Post('content/tags')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth('JWT-auth')
  @ApiOperation({ summary: 'Generate content tags' })
  @ApiResponse({ status: 200, description: 'Generated tags' })
  async generateContentTags(
    @Body() body: { title: string; description: string; genres: string[] },
  ) {
    return this.aiService.generateContentTags(body.title, body.description, body.genres);
  }

  @Post('content/moderate')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth('JWT-auth')
  @ApiOperation({ summary: 'Moderate content' })
  @ApiResponse({ status: 200, description: 'Moderation result' })
  async moderateContent(@Body() body: { text: string }) {
    return this.aiService.moderateContent(body.text);
  }

  @Post('translate')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth('JWT-auth')
  @ApiOperation({ summary: 'Translate text' })
  @ApiResponse({ status: 200, description: 'Translated text' })
  async translateText(
    @Body() body: { text: string; targetLanguage: string },
  ) {
    return this.aiService.translateText(body.text, body.targetLanguage);
  }
}
