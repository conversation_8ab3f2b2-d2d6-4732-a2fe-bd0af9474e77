'use client';

import { useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useAuthStore } from '@/store/auth.store';

export default function Home() {
  const router = useRouter();
  const { isAuthenticated, currentProfile } = useAuthStore();

  useEffect(() => {
    if (!isAuthenticated) {
      router.push('/auth/login');
    } else if (!currentProfile) {
      router.push('/profiles');
    } else {
      router.push('/dashboard');
    }
  }, [isAuthenticated, currentProfile, router]);

  return (
    <div className="min-h-screen bg-black flex items-center justify-center">
      <div className="text-center">
        <div className="w-16 h-16 bg-red-600 rounded-lg flex items-center justify-center mx-auto mb-4">
          <span className="text-white font-bold text-2xl">H</span>
        </div>
        <h1 className="text-white text-2xl font-bold mb-2">HyperFlix</h1>
        <p className="text-gray-400">Loading...</p>
      </div>
    </div>
  );
}
